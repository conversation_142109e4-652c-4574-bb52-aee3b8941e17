{"name": "cms", "version": "1.0.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "wang <bj<PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:dev": "vue-cli-service build --mode dev", "build:prod": "vue-cli-service build", "build:test": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "new": "plop"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"ajv": "^6.10.2", "axios": "0.19.0", "clipboard": "2.0.4", "codemirror": "5.47.0", "driver.js": "0.9.6", "dropzone": "5.5.1", "echarts": "4.2.1", "element-ui": "2.9.1", "file-saver": "2.0.2", "fuse.js": "3.4.5", "js-cookie": "2.2.0", "jsbarcode": "^3.11.6", "jsencrypt": "^3.0.0-rc.1", "jsonlint": "1.6.3", "normalize.css": "8.0.1", "npm": "^6.10.1", "nprogress": "0.2.0", "path-to-regexp": "3.0.0", "print-js": "^1.0.61", "screenfull": "4.2.0", "showdown": "1.9.0", "sortablejs": "1.9.0", "v-viewer": "^1.4.2", "vue": "2.6.10", "vue-barcode": "^1.3.0", "vue-count-to": "1.0.13", "vue-i18n": "8.11.2", "vue-photo-preview": "^1.1.3", "vue-print-nb": "^1.3.0", "vue-router": "3.0.6", "vue-splitpane": "1.0.4", "vuedraggable": "2.21.0", "vuex": "3.1.1", "xlsx": "0.14.3"}, "devDependencies": {"@babel/core": "7.4.5", "@babel/register": "7.4.4", "@vue/cli-plugin-babel": "3.8.0", "@vue/cli-plugin-eslint": "3.8.0", "@vue/cli-plugin-unit-jest": "^3.9.0", "@vue/cli-service": "3.8.0", "@vue/test-utils": "1.0.0-beta.29", "babel-core": "^6.26.3", "babel-eslint": "10.0.1", "babel-jest": "24.8.0", "babel-plugin-dynamic-import-node": "^2.2.0", "chalk": "2.4.2", "chokidar": "3.0.1", "connect": "3.7.0", "eslint": "5.16.0", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "husky": "2.3.0", "lint-staged": "8.1.7", "minimist": "^1.2.5", "mockjs": "1.0.1-beta3", "node-sass": "^4.12.0", "plop": "2.3.0", "prettier": "^1.17.1", "qrcode": "^1.5.3", "runjs": "^4.4.2", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "^2.1.3", "script-loader": "0.7.2", "serve-static": "^1.14.1", "svg-sprite-loader": "^4.1.6", "svgo": "1.2.2", "uglifyjs-webpack-plugin": "^2.2.0", "vue-loader": "^15.7.1", "vue-template-compiler": "2.6.10", "webpack": "^4.36.1"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}