@charset "UTF-8";
#print {
  position: fixed;
  z-index: -999;
  display: none;
  /*test start*/
  /*test end*/
}

.rendering {
  position: fixed;
  display: block;
  width: 210mm;
  visibility: hidden;
  z-index: -999;
}

.A5 {
  width: 210mm;
  height: 148mm;
}

.A1 {
  width: 80mm;
}

.A2 {
  width: 40mm;
  height: 30mm;
}
.A2.print-container {
  font-weight: bold;
  font-size: 8.25pt;
  padding: 3.75pt 3.75pt;
}
.A2.print-container .print_flex {
  align-items: center;
}
.A2.print-container .print_sbline {
  border-bottom: 2px solid #000000;
}
.A2.print-container.print-patient {
  font-size: 10.5pt;
}
.A2.print-container .small-font {
  font-size: 6pt;
}
.A2.print-container .space-between {
  justify-content: space-between;
}
.A2.print-container .print_name {
  margin-top: -4.5pt;
}
.A2.print-container.infusion-label .print_sbline {
  padding-bottom: 2px;
  margin-bottom: 2px;
}
.A2.print-container.infusion-label .A1box {
  height: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.A2.print-container.infusion-label .A1box .print-bottom {
  margin-top: auto;
  border-top: 2px solid #000;
  padding-top: 1.5pt;
}

.text-indent10 {
  text-indent: 7.5pt;
}

.ticket-page {
  width: 241mm;
  height: 140mm;
  color: #408AF4;
  position: relative;
}

.text-overflow {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.print-container {
  color: #333;
  font-size: 10.5pt;
  line-height: 1.5;
  box-sizing: border-box;
  page-break-after: always;
}

.print-header-top {
  position: relative;
  display: flex;
  padding: 0 75pt;
  align-items: flex-end;
  justify-content: space-between;
}
.print-header-top .print-header-top-left, .print-header-top .print-header-top-right {
  position: absolute;
}
.print-header-top .print-header-top-left {
  left: 0;
  bottom: 0;
}
.print-header-top .print-header-top-right {
  right: 0;
  top: 0;
}
.print-header-top .print-header-top-center {
  flex: 1;
  position: relative;
  text-align: center;
}
.print-header-top .print-header-top-name {
  height: 22.5pt;
  font-size: 15pt;
  font-weight: bold;
  margin: 0;
}
.print-header-top .print-header-top-desc {
  height: 18pt;
  font-size: 12pt;
  font-weight: bold;
  margin: 0;
  margin-top: 2.25pt;
}
.print-header-top .print-header-top-logo {
  position: absolute;
  top: 30%;
  left: 15%;
  width: 75pt;
}

.print-header-body-pb3 {
  border-bottom: 2.25pt solid #333;
}

.print-header-body-pb1 {
  border-bottom: 0.75pt solid #333;
}

.print-header-body {
  font-size: 10.5pt;
  padding: 3pt 0;
}
.print-header-body .print-header-title {
  display: flex;
}
.print-header-body .print-header-title .date {
  margin-left: auto;
}
.print-header-body .print-header-title-text {
  display: flex;
  flex: 0 0 25%;
  padding: 3pt 0;
  overflow: hidden;
}
.print-header-body .print-header-title-text .print-header-title-label {
  flex-shrink: 0;
  font-weight: bold;
}
.print-header-body .print-header-title-text.print-header-title-text100 {
  flex: 0 0 100%;
}

.doc-advice {
  padding: 3pt 0;
}

.fixed-advice {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 3pt 0;
}

.doc-advice-text {
  display: flex;
}
.doc-advice-text .title {
  flex-shrink: 0;
  font-weight: bold;
}

.print-footer-body {
  font-size: 10.5pt;
  padding: 7.5pt 0;
  border-top: 1.5pt solid #ccc;
}
.print-footer-body .print-footer-title {
  display: flex;
  justify-content: space-between;
}
.print-footer-body .print-footer-title .date {
  margin-left: auto;
}
.print-footer-body .print-footer-title-text {
  display: flex;
  min-width: 97.5pt;
  padding: 3pt 0;
  overflow: hidden;
}
.print-footer-body .print-footer-title-text .print-footer-title-label {
  flex-shrink: 0;
  font-weight: bold;
}

.print-footer-space-between {
  justify-content: space-between;
}

.footer-between__item {
  flex-grow: 1;
  flex-shrink: 0;
  flex-wrap: wrap;
}

.print-content-container {
  position: relative;
  overflow: hidden;
}

.print-content-body {
  color: #333;
  font-size: 10.5pt;
  line-height: 1.5;
  padding: 3.75pt 0;
}

.content-empty-tip-line {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0.75pt;
  border-bottom: 0.75pt dashed #000;
}

.content-empty-tip-text {
  content: "以下内容为空";
  position: absolute;
  bottom: 0;
  font-size: 12pt;
  margin-left: 50%;
  transform: translate(-50%, 100%);
  background: #fff;
}

.content-empty-tip {
  position: relative;
}

.content-line {
  display: flex;
  padding: 3.75pt 0;
}
.content-line .content-line-title {
  flex-shrink: 0;
  font-weight: bold;
}

.treatment-item, .drug-item, .infusion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 3.75pt 0;
}

.item-gap {
  padding: 0 7.5pt;
}

.renderPrescription .tcm-drug-list, .renderPrescription .drug-item, .renderPrescription .infusion-group {
  border: none !important;
}

.tcm-drug-list {
  margin: 0 -7.5pt;
}
.tcm-drug-list .tcm-drug-item {
  display: inline-block;
  width: 25%;
  padding: 3.75pt 7.5pt;
  text-align: center;
  vertical-align: text-top;
}
.tcm-drug-list .tcm-drug-item:nth-child(4n+1) {
  text-align: left;
}
.tcm-drug-list .tcm-drug-item:nth-child(4n) {
  text-align: right;
}

.tcm-drug-total {
  padding: 3.75pt 0;
}

.print-line {
  border-top: 0.75pt dashed #000;
}

.drug-item span {
  margin-right: 7.5pt;
}
.drug-item span:nth-child(even) {
  flex: 1;
}
.drug-item span:nth-child(odd) {
  flex: 2;
}
.drug-item span:last-child {
  margin-right: 0;
  text-align: right;
}

.infusion-group {
  position: relative;
  display: flex;
  align-items: center;
  padding: 3.75pt 0;
}
.infusion-group .infusion-list {
  width: 75%;
}
.infusion-group .infusion-combine {
  position: absolute;
  top: 17%;
  right: 23.5%;
  width: 7.5pt;
  height: 66%;
  border: 1.5pt solid #333;
  border-radius: 3.75pt;
  border-left: 0;
  border-top: 0;
  border-bottom: 0;
}
.infusion-group .infusion-useMethod {
  width: 22%;
  margin-left: 3%;
}

.cell-header {
  font-weight: bold;
}

.inspect-cell {
  display: flex;
  align-items: center;
  padding: 2.25pt 0;
}
.inspect-cell .inspect-cell-text {
  display: inline-block;
  flex: 1;
}
.inspect-cell .inspect-cell-text:first-child {
  flex: 2;
}

.renderExamination .inspect-cell-text:first-child {
  flex: 3;
}
.renderExamination .inspect-line {
  display: flex;
  padding: 3.75pt 0;
}
.renderExamination .inspect-line .inspect-line-title {
  flex-shrink: 0;
}

.print-table-cell {
  display: flex;
}
.print-table-cell .print-table-cell-text {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 7.5pt;
  border: 1px solid #333;
  box-sizing: border-box;
}
.print-table-cell .print-table-cell-text div + div {
  margin-top: 3.75pt;
}
.print-table-cell .print-table-cell-text .print-table-cell-name {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  word-break: break-all;
}
.print-table-cell .print-table-cell-text .print-table-cell-name .print-table-cell-name-value {
  flex-shrink: 0;
  min-width: 60pt;
  margin-left: 7.5pt;
  text-align: left;
}
.print-table-cell .print-table-cell-text + .print-table-cell-text {
  border-left: none;
}
.print-table-cell + .print-table-cell .print-table-cell-text {
  border-top: none;
}
.print-table-cell:not(.table-header) .print-table-cell-text:first-child {
  align-items: flex-start;
}

.combo-cell:not(.combo-cell + .combo-cell) .print-table-cell-text {
  justify-content: flex-end;
}
.combo-cell .print-table-cell-text {
  padding-bottom: 0;
  border-bottom: none;
}

.renderInfusionInjection .print-table-cell .print-table-cell-text {
  padding: 7.5pt 19.5pt;
}
.renderInfusionInjection .print-table-cell .print-table-cell-text:first-child {
  border-right: none;
  padding-right: 0;
}
.renderInfusionInjection .print-table-cell .print-table-cell-name-label {
  position: relative;
}
.renderInfusionInjection .print-table-cell .print-table-cell-name-label .table-index {
  position: absolute;
  width: 10.5pt;
  top: 2.25pt;
  left: -13.5pt;
  height: 10.5pt;
  border: 0.75pt solid #333;
  border-radius: 50%;
  line-height: 9pt;
  text-align: center;
  font-size: 10.5pt;
  font-style: normal;
}

.menstruation {
  display: inline-flex;
  align-items: center;
}
.menstruation > span {
  margin-right: 6px;
}
.menstruation > .menstruation__period {
  display: inline-flex;
  flex-direction: column;
  text-align: center;
  font-size: 12px;
}
.menstruation > .menstruation__period > .line {
  border-top: 1px solid #333;
}

@page {
  margin: 0;
  padding: 0;
}
.a-space {
  display: inline-block;
  width: 10.5pt;
}

@media print {
  @page {
    margin: 11.25pt 15pt 7.5pt;
    size: A1 landscape;
  }
  #app, #nprogress, .el-popover, .v-modal, .el-notification, .print-loading-wrapper {
    display: none !important;
  }

  #print {
    position: static;
    display: block;
  }

  html, body {
    height: inherit !important;
    padding: 0 !important;
    margin: 0 !important;
    overflow: inherit !important;
    min-width: unset !important;
    min-height: unset !important;
  }
}
.dispensingcode {
  margin: 0 auto;
  display: block;
}

.print_c666 {
  color: #666;
}

.print_tc {
  text-align: center;
}

.print_tr {
  text-align: right;
}

.print_pt10 {
  padding-top: 10px;
}

.print_pt20 {
  padding-top: 20px;
}

.print_pb10 {
  padding-bottom: 10px;
}

.print_mt10 {
  margin-top: 10px;
}

.print_lr10 {
  padding: 0 10px;
}

.print_pb20 {
  padding-bottom: 20px;
}

.print_pl10 {
  padding-left: 10px;
}

.print_strong {
  font-weight: 900;
}

.print_f12 {
  font-size: 12px;
}

.print_f14 {
  font-size: 14px;
}

.print_f16 {
  font-size: 16px;
}

.print_topline {
  border-top: 1px solid #000;
}

.print_indent {
  text-indent: 2em;
}

.print_bline {
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px dashed #000;
}

.print_line__split + .print_line__split {
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px dashed #000;
}

.print_sbline {
  padding-bottom: 5px;
  margin-bottom: 5px;
  border-bottom: 1px solid #ccc;
}

.print_flex {
  display: flex;
  display: -webkit-flex;
}

.print_flex1 {
  flex: 1;
}

.print_flex2 {
  flex: 2;
}

.print_flexwrap {
  display: flex;
  display: -webkit-flex;
  flex-wrap: wrap;
}

.print_flexwrap > div:nth-child(2n+1) {
  margin-right: 1%;
}

.print_flexwrap > div:nth-child(2n) {
  margin-left: 1%;
}

.print_center {
  margin: 0 auto;
  text-align: center;
}

.flex_lr {
  /* 水平两端对齐，剩余空间平均分布 */
  display: flex;
  justify-content: space-between;
}

.barcode .vue-barcode-element {
  vertical-align: middle;
}

.print_flexwrap > div {
  width: 48%;
  flex-shrink: 0;
}

.pring_w40 {
  width: 40px;
  flex-shrink: 0;
  overflow: hidden;
}

.print_w20 {
  width: 20px;
  flex-shrink: 0;
  overflow: hidden;
}

.print_pl2 {
  padding-left: 2%;
}

.pring_w60 {
  width: 60px;
  flex-shrink: 0;
  overflow: hidden;
}

.pring_w70 {
  width: 70px;
  flex-shrink: 0;
  padding-right: 5px;
  overflow: hidden;
}

.print_report2 {
  max-width: 50%;
  flex-grow: 1;
  flex-shrink: 0;
}

.print_report3 {
  max-width: 60px;
  flex-grow: 1;
  flex-shrink: 0;
}

.print_report4 {
  max-width: 13%;
  flex-grow: 1;
  flex-shrink: 0;
}

.print_report5 {
  width: 20px;
  padding-right: 30px;
  flex-shrink: 0;
}

.pring_w80 {
  max-width: 20%;
  flex-grow: 1;
  overflow: hidden;
}

.pring_w100 {
  width: 100px;
  flex-shrink: 0;
  overflow: hidden;
}

.print_fw50 > div {
  width: 50%;
  float: left;
  flex-shrink: 0;
}
.print_fw50 > div .print_report3 {
  max-width: 60px;
  flex-grow: 1;
  flex-shrink: 0;
}
.print_fw50 > div .print_report4 {
  max-width: 18%;
  flex-grow: 1;
  flex-shrink: 0;
}

.print_report_line {
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  line-height: 1.8;
}

.report-left-line {
  border-left: 1px solid #000;
}

.report-body {
  line-height: 1.4;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
  /*不用有内容也可以*/
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
}

/*header start*/
.print-header-body .headreport {
  flex: 1;
}

.reportbody {
  border-top: 1px solid #000;
}

.text-indent20 {
  text-indent: 15pt;
}

.text-ellipsis {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
  text-overflow: ellipsis;
}

.medical-list .print-content-body {
  height: 100%;
}
.medical-list .print-table__header {
  border-bottom: 1px solid #333;
}
.medical-list .print-table_header__left, .medical-list .print-table_body__left {
  width: 180pt;
  border-right: 1px solid #333;
  box-sizing: border-box;
  text-align: center;
}
.medical-list .print-table_header__left .print-cell__item, .medical-list .print-table_body__left .print-cell__item {
  display: inline-block;
  flex-grow: 1;
  width: 50%;
}
.medical-list .print-table_header__right, .medical-list .print-table_body__right {
  flex-grow: 1;
  padding-left: 15pt;
  box-sizing: border-box;
}
.medical-list .print-table_header__right .print-cell__item, .medical-list .print-table_body__right .print-cell__item {
  display: inline-block;
  flex-grow: 1;
  flex-shrink: 0;
  max-width: 15%;
  text-align: center;
}
.medical-list .print-table_header__right .print-cell__item:nth-child(1), .medical-list .print-table_body__right .print-cell__item:nth-child(1) {
  min-width: 25%;
  max-width: 25%;
  text-align: left;
}
.medical-list .print-table__body {
  height: 100%;
  flex-grow: 1;
}
.medical-list .print-row__item {
  width: 100%;
  justify-content: space-between;
}

.print-ticket__wrap {
  width: 241mm;
  height: 140mm;
  color: #408AF4;
  position: relative;
  font-size: 10.5pt;
}

.print-ticket-inner__text {
  z-index: 4;
  position: absolute;
  left: 30pt;
  top: 48pt;
}
.print-ticket__header {
  margin-bottom: 4.5pt;
}
.print-ticket__header .print-header__left {
  word-break: keep-all;
}
.print-ticket__header .print-header__left span {
  display: inline-block;
  text-indent: 64.5pt;
  height: 17.25pt;
  vertical-align: middle;
}
.print-ticket__header .print-header__left span:nth-child(3n+1) {
  width: 115.5pt;
}
.print-ticket__header .print-header__left span:nth-child(3n+2) {
  width: 232.5pt;
  text-indent: 56.25pt;
}
.print-ticket__header .print-header__left span:nth-child(3n) {
  width: 270pt;
  text-indent: 45pt;
}
.print-ticket__header .print-header__right {
  position: absolute;
  right: 40.5pt;
  top: 15.75pt;
}
.print-ticket__header .print-header__right span {
  display: inline-block;
  width: 48pt;
}

.print-ticket__container span {
  display: inline-block;
  line-height: 26.25pt;
  box-sizing: border-box;
}
.print-ticket__container .base-ticket__name {
  width: 201.75pt;
  text-indent: 67.5pt;
}
.print-ticket__container .base-ticket__type {
  padding: 0 3.75pt;
  width: 153.75pt;
  display: inline-block;
}
.print-ticket__container .base-ticket__type.base-ticket__gender {
  width: 55.5pt;
  margin-left: 60pt;
}
.print-ticket__container .base-ticket__type.base-ticket__gender .true {
  margin-right: 22.5pt;
}
.print-ticket__container .base-ticket__type .true {
  display: inline-block;
  transform: translateY(-6px);
  margin-right: 43.5pt;
}
.print-ticket__container .base-ticket__type .true:last-child {
  margin-right: 0;
}
.print-ticket__container .base-ticket__money {
  width: 187.5pt;
  text-indent: 136px;
}
.print-ticket__container .base-ticket__cast {
  width: 133.5pt;
  text-indent: 79.5pt;
}
.print-ticket__container .print-ticket__table {
  height: 25.5pt;
  line-height: 25.5pt;
}
.print-ticket__container .print-ticket__table span.ticket-total__price {
  width: 438.75pt;
  text-align: right;
}
.print-ticket__container .print-ticket__table span.ticket-total__price-number {
  width: 150pt;
  text-align: left;
  text-indent: 42pt;
}
.print-ticket__container .print-ticket__footer {
  margin-top: 51pt;
}
.print-ticket__container .print-ticket__footer span {
  width: 133.5pt;
  text-indent: 41.25pt;
  text-align: left;
  vertical-align: top;
}
.print-ticket__container .print-ticket__footer span:nth-child(1) {
  width: 100.5pt;
  margin-left: 93pt;
  text-indent: 0;
  height: auto;
  padding-top: 3.75pt;
  line-height: 15pt;
}
.print-ticket__container .print-ticket__footer span:nth-child(2) {
  width: 150pt;
}
.print-ticket__container .print-ticket__footer span:nth-child(3) {
  width: 150pt;
  text-indent: 26.25pt;
}
.print-ticket__container .capital {
  font-style: normal;
  display: inline-block;
  width: 35.25pt;
  text-align: center;
  box-sizing: border-box;
  margin-right: 3pt;
}

.print-ticket-inner__text .print-ticket__hidden {
  visibility: hidden;
}
.print-ticket-inner__text .print-table__column {
  line-height: 26.25pt;
  box-sizing: border-box;
  display: flex;
}
.print-ticket__column {
  display: flex;
  flex-direction: column;
}
.print-ticket__column .print-column__item {
  display: flex;
}
.print-ticket__column .print-column__item span {
  display: inline-block;
  text-align: center;
  flex: 1;
}
.print-ticket__column .print-column__item span:first-child {
  width: 75pt;
}
.print-ticket__column:nth-child(1) {
  width: 130.5pt;
}
.print-ticket__column:nth-child(1) span:first-child {
  width: 60pt;
}
.print-ticket__column:nth-child(2) {
  width: 154.5pt;
}
.print-ticket__column:nth-child(3) {
  width: 162pt;
}
.print-ticket__column:nth-child(4) {
  width: 154.5pt;
}

.print-ticket-inner__text .print-table__site {
  min-height: 78pt;
}

.delivery-medical .tcm-drug-item {
  padding-bottom: 3.75pt;
}
.delivery-medical .drug-item {
  border-top: none !important;
}
.delivery-medical .content-line {
  border-top: 0.75pt dashed #000;
}
.delivery-medical .tcm-tip {
  font-size: 9pt;
}

.print-pre-oh {
  white-space: nowrap;
  overflow: hidden;
}