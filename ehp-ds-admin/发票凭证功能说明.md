# 发票凭证上传功能说明

## 功能概述

在发货信息页面的物流单号下方新增了"发票凭证"上传功能，支持本地上传和扫码上传两种方式。

## 主要特性

### 1. 本地上传
- 点击"本地上传"按钮选择本地图片文件
- 支持格式：.png、.jpg、.jpeg
- 文件大小限制：单个文件不超过500KB
- 数量限制：最多上传4张图片

### 2. 扫码上传
- 点击"扫码上传"按钮生成二维码
- 使用手机扫描二维码，打开移动端上传页面
- 在手机上选择图片进行上传
- PC端自动同步显示上传的图片

### 3. 图片管理
- 支持图片预览（点击图片查看大图）
- 支持删除图片（鼠标悬停显示删除按钮）
- 实时显示上传进度和状态

## 技术实现

### PC端功能
- 使用Element UI的Upload组件
- 集成QRCode库生成二维码
- localStorage实现PC端和移动端数据同步
- 轮询检测移动端上传的文件

### 移动端页面
- 响应式设计，适配移动设备
- 友好的触摸操作体验
- 自动关闭页面功能

### 数据流程
1. PC端生成包含订单ID和token的二维码URL
2. 移动端扫码后打开专用上传页面
3. 移动端上传完成后将文件信息存储到localStorage
4. PC端轮询检测localStorage中的新文件
5. 自动同步显示上传的图片

## 文件结构

```
src/
├── views/
│   ├── order/
│   │   └── sendgood.vue          # PC端发货页面（包含发票凭证功能）
│   └── mobile/
│       └── upload.vue            # 移动端上传页面
└── router/
    └── index.js                  # 路由配置
```

## 使用方法

### PC端操作
1. 进入订单发货页面
2. 填写物流公司和物流单号
3. 在"发票凭证"区域选择上传方式：
   - **本地上传**：直接选择本地文件
   - **扫码上传**：使用手机扫描二维码

### 移动端操作
1. 扫描PC端生成的二维码
2. 在移动端页面选择图片
3. 确认上传
4. 等待PC端自动同步

## 注意事项

1. 图片格式限制：仅支持 .png、.jpg、.jpeg 格式
2. 文件大小限制：单个文件不能超过500KB
3. 数量限制：最多只能上传4张图片
4. 网络要求：移动端和PC端需要在同一网络环境下（或能访问相同的服务器）
5. 浏览器兼容性：建议使用现代浏览器（Chrome、Safari、Firefox等）

## 依赖说明

- **qrcode**: 用于生成二维码
- **Element UI**: 提供UI组件
- **localStorage**: 实现跨页面数据传输

## 安装依赖

```bash
npm install qrcode
```

## 后续优化建议

1. 可以考虑使用WebSocket实现实时同步，替代轮询机制
2. 可以增加图片压缩功能，自动将大文件压缩到限制范围内
3. 可以增加图片裁剪功能，统一图片尺寸
4. 可以增加批量上传进度显示
5. 可以增加上传历史记录功能 