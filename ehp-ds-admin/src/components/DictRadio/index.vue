<template>
  <el-radio-group v-model="value">
    <el-radio
      v-for="item in dictData"
      :key="item.code"
      :label="item.code"
      :value="item.code"
    >{{ item.value }}</el-radio>
  </el-radio-group>
</template>

<script>
import { getDict } from '@/api/system/dict'
export default {
  name: 'DictRadio',
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: null
    },
    type: {
      type: String,
      required: true
    },
    placeholder: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      dictData: []
    }
  },
  watch: {
    value: function(val) {
      this.$emit('change', val)
    }
  },
  mounted() {
    this.getDict()
  },
  methods: {
    // 获取数据
    getDict() {
      getDict(this.type).then(response => {
        this.dictData = response
      })
    }
  }
}
</script>
