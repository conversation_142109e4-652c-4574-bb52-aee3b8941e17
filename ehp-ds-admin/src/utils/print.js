import printJS from 'print-js'
import { getToken, getTokenName } from '@/utils/auth'
import { isWindows } from '@/utils/index'
import LodopAPI from '@/utils/LodopFuncs' // 导入模块
export function print(url) {
  var printUrl = process.env.VUE_APP_BASE_API + url + '?' + getTokenName() + '=' + getToken()
  printJS(printUrl)
}

export function printPage(params) {
  console.log(params, '==========printPage==========')
  if (isWindows()) {
    if (LodopAPI.getLodop()) {
      params.htmls = []
      params.elements.forEach((item) => {
        item.appendChild(getStyle())
        params.htmls.push(item.outerHTML)
        // writeIframe(item.outerHTML)
      })

      LodopAPI.print(params)
    } else {
      this.$alert('<div>请安装打印机插件!<a style="margin-left: 10px;color: #7cd184;" href="https://cis-static-dev.oss-cn-shanghai.aliyuncs.com/abc-print/temp/CLodop_Setup_for_Win32NT.exe" target="view_window">点击下载</a></div>', '提示', {
        dangerouslyUseHTMLString: true
      })
    }
  } else {
    window.print()
  }
}

function getStyle() {
  const nod = document.createElement('link')
  nod.href = '/static/css/print.css'
  nod.rel = 'stylesheet'
  nod.type = 'text/css'
  nod.ref = 'stylesheet'
  return nod
}
