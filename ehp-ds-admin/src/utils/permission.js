import store from '@/store'

export default function hasPermission(value) {
  const roles = store.getters && store.getters.roles
  const hasAdmin = roles.some(role => {
    return 'admin'.includes(role)
  })
  if (hasAdmin) {
    // return true
  }

  if (value && value instanceof Array && value.length > 0) {
    const userPermissions = store.getters && store.getters.permissions
    const permission = value
    const hasPermission = userPermissions.some(dbpermission => {
      return permission.includes(dbpermission)
    })

    if (!hasPermission) {
      return false
    }
    return true
  } else {
    console.error(`当前操作没有权限`)
    return false
  }
}
