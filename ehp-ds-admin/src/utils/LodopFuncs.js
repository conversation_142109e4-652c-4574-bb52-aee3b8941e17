﻿// ==本JS是加载Lodop插件及CLodop服务的综合示例，可直接使用，建议看懂后融进自己页面程序==

var CreatedOKLodopObject, CLodopIsLocal, CLodopJsState

// ==判断是否需要CLodop(那些不支持插件的浏览器):==
function needCLodop() {
  try {
    var ua = navigator.userAgent
    if (ua.match(/Windows\sPhone/i)) { return true }
    if (ua.match(/iPhone|iPod|iPad/i)) { return true }
    if (ua.match(/Android/i)) { return true }
    if (ua.match(/Edge\D?\d+/i)) { return true }

    var verTrident = ua.match(/Trident\D?\d+/i)
    var verIE = ua.match(/MSIE\D?\d+/i)
    var verOPR = ua.match(/OPR\D?\d+/i)
    var verFF = ua.match(/Firefox\D?\d+/i)
    var x64 = ua.match(/x64/i)
    if ((!verTrident) && (!verIE) && (x64)) { return true } else if (verFF) {
      verFF = verFF[0].match(/\d+/)
      if ((verFF[0] >= 41) || (x64)) { return true }
    } else if (verOPR) {
      verOPR = verOPR[0].match(/\d+/)
      if (verOPR[0] >= 32) { return true }
    } else if ((!verTrident) && (!verIE)) {
      var verChrome = ua.match(/Chrome\D?\d+/i)
      if (verChrome) {
        verChrome = verChrome[0].match(/\d+/)
        if (verChrome[0] >= 41) { return true }
      }
    }
    return false
  } catch (err) {
    return true
  }
}

// 加载CLodop时用双端口(http是8000/18000,而https是8443/8444)以防其中某端口被占,
// 主JS文件“CLodopfuncs.js”是固定文件名，其内容是动态的，与当前打印环境有关:
function loadCLodop() {
  if (CLodopJsState === 'loading' || CLodopJsState === 'complete') return
  CLodopJsState = 'loading'
  var head = document.head || document.getElementsByTagName('head')[0] || document.documentElement
  var JS1 = document.createElement('script')
  var JS2 = document.createElement('script')

  if (window.location.protocol === 'https:') {
    JS1.src = 'https://localhost.lodop.net:8443/CLodopfuncs.js'
    JS2.src = 'https://localhost.lodop.net:8444/CLodopfuncs.js'
  } else {
    JS1.src = 'http://localhost:8000/CLodopfuncs.js'
    JS2.src = 'http://localhost:18000/CLodopfuncs.js'
  }
  JS1.onload = JS2.onload = function() { CLodopJsState = 'complete' }
  JS1.onerror = JS2.onerror = function(evt) { CLodopJsState = 'complete' }
  head.insertBefore(JS1, head.firstChild)
  head.insertBefore(JS2, head.firstChild)
  CLodopIsLocal = !!((JS1.src + JS2.src).match(/\/\/localho|\/\/127.0.0./i))
}

if (needCLodop()) { loadCLodop() }// 开始加载

// ==获取LODOP对象主过程,判断是否安装、需否升级:==
function getLodop(oOBJECT, oEMBED) {
  const strHtmInstall = "<br><span >打印控件未安装!点击这里<a href='install_lodop32.exe' target='_self'>执行安装</a>,安装后请刷新页面或重新进入。</span>"
  const strHtmUpdate = "<br><span >打印控件需要升级!点击这里<a href='install_lodop32.exe' target='_self'>执行升级</a>,升级后请重新进入。</span>"
  const strHtm64_Install = "<br><span >打印控件未安装!点击这里<a href='install_lodop64.exe' target='_self'>执行安装</a>,安装后请刷新页面或重新进入。</span>"
  const strHtm64_Update = "<br><span >打印控件需要升级!点击这里<a href='install_lodop64.exe' target='_self'>执行升级</a>,升级后请重新进入。</span>"
  const strHtmFireFox = '<br><br><span >（注意：如曾安装过Lodop旧版附件npActiveXPLugin,请在【工具】->【附加组件】->【扩展】中先卸它）</span>'
  const strHtmChrome = '<br><br><span >(如果此前正常，仅因浏览器升级或重安装而出问题，需重新执行以上安装）</span>'
  const strCLodopInstall_1 = "<br><span >Web打印服务CLodop未安装启动，点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>下载执行安装</a>"
  const strCLodopInstall_2 = "<br>（若此前已安装过，可<a href='CLodop.protocol:setup' target='_self'>点这里直接再次启动</a>）"
  const strCLodopInstall_3 = '，成功后请刷新或重启浏览器。</span>'
  const strCLodopUpdate = "<br><span >Web打印服务CLodop需升级!点击这里<a href='CLodop_Setup_for_Win32NT.exe' target='_self'>执行升级</a>,升级后请刷新或重启浏览器。</span>"
  const tipBody = document.createElement('div')
  let LODOP
  try {
    const ua = navigator.userAgent
    const isIE = !!(ua.match(/MSIE/i)) || !!(ua.match(/Trident/i))
    if (needCLodop()) {
      try {
        // eslint-disable-next-line no-undef
        LODOP = getCLodop()
      } catch (err) {
        console.log(err)
      }
      if (!LODOP && CLodopJsState !== 'complete') {
        if (CLodopJsState === 'loading') { alert('网页还没下载完毕，请稍等一下再操作.') } else { alert('没有加载CLodop的主js，请先调用loadCLodop过程.') }
        return
      }
      if (!LODOP) {
        tipBody.innerHTML = strCLodopInstall_1 + (CLodopIsLocal ? strCLodopInstall_2 : '') + strCLodopInstall_3 + tipBody.innerHTML
        console.log(tipBody.innerHTML)
        return
      } else {
        // eslint-disable-next-line no-undef
        if (CLODOP.CVERSION < '*******') {
          tipBody.innerHTML = strCLodopUpdate + tipBody.innerHTML
        }
        if (oEMBED && oEMBED.parentNode) { oEMBED.parentNode.removeChild(oEMBED) } // 清理旧版无效元素
        if (oOBJECT && oOBJECT.parentNode) { oOBJECT.parentNode.removeChild(oOBJECT) }
      }
    } else {
      var is64IE = isIE && !!(ua.match(/x64/i))
      // ==如果页面有Lodop就直接使用,否则新建:==
      if (oOBJECT || oEMBED) {
        if (isIE) { LODOP = oOBJECT } else { LODOP = oEMBED }
      } else if (!CreatedOKLodopObject) {
        LODOP = document.createElement('object')
        LODOP.setAttribute('width', 0)
        LODOP.setAttribute('height', 0)
        LODOP.setAttribute('style', 'position:absolute;left:0px;top:-100px;width:0px;height:0px;')
        if (isIE) { LODOP.setAttribute('classid', 'clsid:2105C259-1E0C-4534-8141-A753534CB4CA') } else { LODOP.setAttribute('type', 'application/x-print-lodop') }
        document.documentElement.appendChild(LODOP)
        CreatedOKLodopObject = LODOP
      } else { LODOP = CreatedOKLodopObject }
      // ==Lodop插件未安装时提示下载地址:==
      if ((!LODOP) || (!LODOP.VERSION)) {
        if (ua.indexOf('Chrome') >= 0) { tipBody.innerHTML = strHtmChrome + tipBody.innerHTML }
        if (ua.indexOf('Firefox') >= 0) { tipBody.innerHTML = strHtmFireFox + tipBody.innerHTML }
        tipBody.innerHTML = (is64IE ? strHtm64_Install : strHtmInstall) + tipBody.innerHTML
        console.log(tipBody.innerHTML)
        return LODOP
      }
    }
    if (LODOP.VERSION < '*******') {
      if (!needCLodop()) { tipBody.innerHTML = (is64IE ? strHtm64_Update : strHtmUpdate) + tipBody.innerHTML }
    }
    console.log(tipBody.innerHTML)
    // ===如下空白位置适合调用统一功能(如注册语句、语言选择等):==

    // =======================================================
    return LODOP
  } catch (err) {
    alert('getLodop出错:' + err)
  }
}
/**
 *
 * @param {*} params LODOP：打印对象，printIndex：打印机编号，
 * orient:1---纵(正)向打印，固定纸张；
 *    2---横向打印，固定纸张；
 *    3---纵(正)向打印，宽度固定，高度按打印内容的高度自适应；
 *    0(或其它)----打印方向由操作者自行选择或按打印机缺省设置；
 * pageType:纸张类型，count：份数，html，内容
 */
export function print(params) {
  const LODOP = getLodop()
  LODOP.PRINT_INIT(params.title ? params.title : '打印')
  // 选择打印机
  LODOP.SET_PRINTER_INDEXA(params.printIndex)
  if (!params.PageWidth) {
    params.PageWidth = 0
  }
  if (!params.PageHeight) {
    params.PageHeight = 0
  }

  // 选择纸张
  LODOP.SET_PRINT_PAGESIZE(params.orient, params.PageWidth, params.PageHeight, params.pageType)
  // 设置打印份数
  LODOP.SET_PRINT_COPIES(params.count)
  // console.log(params.html)
  // 距离上，距离左边
  for (let i = 0; i < params.htmls.length; i++) {
    LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', params.htmls[i])
    LODOP.NEWPAGEA()
  }
  if (params.widthOverflow && params.widthOverflow === 2) {
    params.widthOverflow = false
  } else {
    params.widthOverflow = true
  }
  // LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', params.html)
  LODOP.SET_PRINT_MODE('FULL_WIDTH_FOR_OVERFLOW', params.widthOverflow)
  LODOP.SET_PRINT_MODE('FULL_HEIGHT_FOR_OVERFLOW', true)
  // LODOP.SET_PRINT_MODE('PRINTSETUP_PERCENT', 15)

  // LODOP.SELECT_PRINTER()
  // 打印
  // LODOP.PRINT()
  // LODOP.PRINTA() //选择打印机
  // LODOP.PRINTB() //强制用缺省布局风格来打印
  // 打印预览
  LODOP.PREVIEWA()
}

export function getPrinterAndPagesSizeList() {
  const LODOP = getLodop()
  if (!LODOP) return false
  const count = LODOP.GET_PRINTER_COUNT()
  const printers = []
  for (let index = 0; index < count; index++) {
    const printer = {}
    printer.index = index
    printer.name = LODOP.GET_PRINTER_NAME(index)
    printer.pagesSizes = LODOP.GET_PAGESIZES_LIST(index, ',').split(',')
    printers.push(printer)
  }
  return printers
}

export default { getLodop, print, getPrinterAndPagesSizeList } // 导出getLodop
