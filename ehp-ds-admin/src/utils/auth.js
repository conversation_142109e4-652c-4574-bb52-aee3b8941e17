import Cookies from 'js-cookie'

const TokenName = 'tokenName'
const TokenKey = 'tokenValue'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function removeTokenName() {
  return Cookies.remove(TokenName)
}

export function getTokenName() {
  return Cookies.get(TokenName)
}

export function setTokenName(tokenName) {
  return Cookies.set(TokenName, tokenName)
}
