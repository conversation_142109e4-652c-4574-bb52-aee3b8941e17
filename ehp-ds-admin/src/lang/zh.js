export default {
  route: {
    dashboard: '首页',
    systemManagement: '系统管理',
    userManagement: '用户管理',
    deptManagement: '部门管理',
    roleManagement: '角色管理',
    menuManagement: '菜单管理',
    dictManagement: '字典管理',
    sysLog: '系统日志',
    mpUserManagement: '前台用户管理',
    wxMpUserManagement: '微信用户',
    couponsManagement: '优惠券',
    bmsManagement: '结算管理',
    wmsManagement: '仓储管理',
    omsManagement: '订单管理',
    warehouseList: '仓库列表',
    productList: '商品列表',
    orderList: '订单列表',
    returnOrderList: '退货列表',
    detectionList: '检测列表',
    companyList: '公司提成',
    storeList: '门店提成',
    userList: '用户提成',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON 编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链'
  },
  navbar: {
    logOut: '退出登录',
    dashboard: '首页',
    github: '项目地址',
    theme: '换肤',
    size: '布局大小',
    updatePwd: '修改密码'
  },
  login: {
    title: '药店发货平台',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！'
  },
  sysUser: {
    userId: '用户ID',
    username: '用户名称',
    name: '真实姓名',
    deptName: '所属部门',
    email: '邮箱',
    mobile: '手机号',
    status: '状态',
    createdAt: '创建时间',
    newPassword: '新密码',
    role: '角色',
    password: '密码'
  },
  sysRole: {
    id: '角色ID',
    roleName: '角色名称',
    deptName: '部门名称',
    remark: '备注',
    createdAt: '创建时间',
    menuIdList: '功能权限'
  },
  sysDict: {
    name: '字典名称',
    type: '字典类型',
    code: '字典码',
    value: '字典值',
    orderNum: '排序',
    remark: '备注',
    createdAt: '创建时间'
  },
  sysLog: {
    id: 'id',
    username: '用户名',
    operation: '用户操作',
    method: '请求方法',
    params: '请求参数',
    time: '执行时长(ms)',
    ip: 'IP地址',
    createdAt: '创建时间'
  },
  sysDept: {
    name: '部门名称',
    parentName: '上级部门',
    orderNum: '排序号',
    createdAt: '创建时间'
  },
  sysMenu: {
    name: '菜单名称',
    type: '类型',
    hidden: '是否隐藏',
    orderNum: '排序号',
    parentId: '上级菜单',
    url: '菜单URL',
    perms: '权限标识',
    icon: '菜单图标',
    createdAt: '创建时间'
  },
  doctor: {
    id: '用户ID',
    name: '用户名称',
    type: '类型',
    phone: '手机',
    hospitalName: '执业医院',
    departmentName: '科室',
    hospitalCityName: '医院所在地',
    gender: '性别',
    headUrl: '头像',
    storeName: '门店名称',
    accountStatus: '账户状态',
    status: '认证状态',
    recordStatus: '备案状态',
    createdAt: '注册时间'
  },
  patient: {
    id: 'ID',
    name: '姓名',
    nickName: '昵称',
    phone: '手机',
    age: '年龄',
    gender: '性别',
    cityId: '城市',
    cancel: '取消关注状态',
    recomDoctor: '上级医生ID',
    headUrl: '头像',
    recomDoctorName: '上级医生姓名',
    type: '类型',
    createdAt: '注册时间',
    loginAddr: '上次登录地址',
    status: '认证状态',
    available: '状态'
  },
  product: {
    list: {
      id: 'ID',
      number: '商品编码',
      type: '品类',
      name: '商品名称',
      commonName: '通用名',
      nmpaType: '安全分类',
      classificationName: '药理分类',
      approvalNumber: '批准文号/注册码',
      createdAt: '建码时间',
      productionEnterprise: '生产企业',
      relationSku: '关联sku',
      dataIntegrity: '资料完整',
      actions: '操作',
      edit: '编辑资料',
      mange: '管理sku'
    },
    mange: {
      add: '新建商品'
    },
    listplaceholder: {}
  },
  wmsWarehouse: {
    id: 'ID',
    name: '仓库名称',
    address: '仓库地址',
    phone: '手机号',
    linkMan: '联系人',
    createdAt: '创建时间'
  },
  omsDetection: {
    sn: '检测样本编号',
    detectionName: '检测样本用户名',
    userId: '用户ID',
    userName: '用户名',
    statusDescribe: '状态',
    applyOrderSn: '取样单号',
    deliveryId: '物流单编号',
    pickupStartTime: '取件开始时间',
    pickupEndTime: '取件结束时间',
    receiver: '收货人',
    address: '详细地址',
    phone: '电话',
    applyStatusDescribe: '取样单状态',
    createdAt: '创建时间'
  },
  omsOrder: {
    orderSn: '订单号',
    phone: '手机号',
    userId: '用户ID',
    userName: '用户名',
    totalAmount: '商品总价',
    freight: '邮费',
    couponPay: '优惠券',
    realPay: '实付',
    orderStatusDescribe: '订单状态',
    payStatusDescribe: '支付状态',
    payTime: '支付时间',
    sendTime: '发货时间',
    createdAt: '订单时间'
  },
  omsReturnOrder: {
    orderSn: '订单号',
    phone: '手机号',
    returnOrderSn: '退货单号',
    userId: '用户ID',
    userName: '用户名',
    orderSourceDescribe: '订单来源',
    statusDescribe: '订单状态',
    deliveryId: '物流单号',
    remark1: '客服备注',
    remark2: '仓库备注',
    createdAt: '申请时间'
  },
  wmsProduct: {
    id: '商品ID',
    number: '商品编码',
    name: '商品名称',
    commonName: '通用名',
    nmpaType: '药监局分类',
    materialType: '原料分类',
    introduce: '商品简介',
    manufactureId: '生产厂商',
    valid: '是否有效',
    status: '状态',
    returnService: '退换货政策',
    packingUnit: '外包装单位',
    packingUnitLimit: '最小包装单位',
    specUnit: '包装规格单位',
    specValue: '商品规格',
    categoryId: '商品分类',
    changedBy: '修改人',
    changedAt: '修改时间'
  },
  wmsSku: {
    id: 'ID',
    code: '规格编码',
    name: '通用名',
    introduce: '简介',
    packingSpec: '包装规格',
    packingUnitNumber: '单位包装规格数量',
    specUnit: '规格单位',
    specValue: '商品规格',
    salePrice: '售价',
    totalSale: '累计售出',
    normalQuantity: '库存数量',
    freeznQuantity: '剩余库存',
    weight: '重量',
    status: '状态',
    changedBy: '修改人',
    changedAt: '修改时间'
  },
  wmsAttribute: {
    attributeId: '商品属性',
    introduceKey: '说明书内显示名',
    introduce: '是否说明书属性',
    attributeKey: '属性名',
    attributeValue: 'Value',
    description: '描述',
    weight: '排序',
    changedAt: '修改时间'
  },
  settleCompany: {
    companyName: '公司名称',
    name: '门店名称',
    saleAmount: '销售金额',
    amount: '提成金额',
    status: '结算状态',
    createdAt: '创建时间'
  },
  settleStore: {
    companyName: '公司名称',
    name: '门店名称',
    saleAmount: '销售金额',
    amount: '提成金额',
    status: '结算状态',
    createdAt: '创建时间'
  },
  settleUser: {
    userId: '用户ID',
    name: '用户姓名',
    nickname: '用户昵称',
    saleAmount: '销售金额',
    amount: '提成金额',
    status: '结算状态',
    createdAt: '创建时间'
  },
  commissionUser: {
    userId: '购买用户ID',
    saleUserId: '销售用户ID',
    saleUserName: '销售用户名称',
    userName: '购买用户名称',
    companyId: '公司ID',
    storeId: '门店ID',
    id: 'ID',
    companyName: '公司名称',
    storeName: '门店',
    orderSn: '订单编号',
    realPay: '销售金额',
    amount: '提成金额',
    canUnlock: '是否锁定',
    expectedUnlock: '预计解锁时间',
    status: '状态',
    createdAt: '创建时间'
  },
  medication: {
    id: '处方ID',
    hospitalName: '处方笺标题',
    doctorId: '医生ID',
    doctorName: '医生姓名',
    patientId: '患者ID',
    patientName: '患者姓名',
    orderId: '订单ID',
    orderSn: '订单号',
    orderAmount: '订单金额',
    serialNumber: '处方笺编号',
    doctorSignStatus: '医生电子签名状态',
    doctorSignTime: '医生签名时间',
    pharmacistName: '药师姓名',
    pharmacistSignStatus: '药师电子签名状态',
    pharmacistSignTime: '药师电子签名时间',
    diagnosis: '临床诊断',
    doctorPhone: '医生电话',
    patientPhone: '患者电话'
  },
  store: {
    skuNumber: '商品sku码',
    skuName: '商品名称',
    warehouseName: '仓库名'
  },
  table: {
    search: '搜索',
    add: '添加',
    export: '导出',
    id: '序号',
    actions: '操作',
    edit: '编辑',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  }
}
