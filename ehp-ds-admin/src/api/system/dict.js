import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/sys/dict/list',
    method: 'get',
    params: data
  })
}

export function get(id) {
  return request({
    url: '/sys/dict/info/' + id,
    method: 'get'
  })
}

export function create(data) {
  return request({
    url: 'sys/dict/save',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: 'sys/dict/update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: '/sys/dict/delete',
    method: 'post',
    data
  })
}
export function getDict(type) {
  return request({
    url: '/sys/dict/vue/list/' + type,
    method: 'get'
  })
}
