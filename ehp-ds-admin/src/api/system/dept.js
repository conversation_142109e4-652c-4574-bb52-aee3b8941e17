import request from '@/utils/request'
// 部门列表分页
export function getList(data) {
  return request({
    url: '/sys/dept/list',
    method: 'get',
    params: data
  })
}
// 选择部门，这里会手动放置一个顶级部门
export function getDeptList() {
  return request({
    url: '/sys/dept/select',
    method: 'get'
  })
}

export function get(data) {
  return request({
    url: '/sys/dept/info/' + data,
    method: 'get'
  })
}

export function create(data) {
  return request({
    url: 'sys/dept/save',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: 'sys/dept/update',
    method: 'post',
    data
  })
}

export function del(data) {
  return request({
    url: '/sys/dept/delete/' + data,
    method: 'get'
  })
}
