import request from '@/utils/request'

export function orderList(data) {
  return request({
    url: '/statistics/order/list',
    method: 'get',
    params: data
  })
}

export function productList(data) {
  return request({
    url: '/statistics/sku/list',
    method: 'get',
    params: data
  })
}

export function orderExport() {
  return process.env.VUE_APP_BASE_API + '/statistics/order/export?'
}

export function productExport() {
  return process.env.VUE_APP_BASE_API + '/statistics/sku/export?'
}

export default {
  orderList,
  productList,
  orderExport,
  productExport
}
