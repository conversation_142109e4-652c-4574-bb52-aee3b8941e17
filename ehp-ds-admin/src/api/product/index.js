import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/pharmacy/warehouse/goods/list',
    method: 'get',
    params: data
  })
}

export function stock(data) {
  return request({
    url: '/pharmacy/warehouse/goods',
    method: 'post',
    data
  })
}

export function excelstock(data) {
  return request({
    url: '/pharmacy/warehouse/goods/bacth/update',
    method: 'post',
    data
  })
}

export function pharmacology() {
  return request({
    url: '/classification/pharmacology/list',
    method: 'get'
  })
}

export default {
  list,
  stock,
  excelstock,
  pharmacology
}
