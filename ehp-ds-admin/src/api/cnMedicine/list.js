import request from '@/utils/request'

/** 中药配方颗粒 列表**/
export const particleList = (data) => {
  return request({
    url: '/tcm/particle',
    method: 'get',
    params: data
  })
}
/** 中药颗粒详情 ***/
export const particleInfo = (id) => {
  return request({
    url: '/tcm/particle/' + id,
    method: 'get'
  })
}
/** 中药配方颗粒 设置价格 **/
export const setPrice = (data) => {
  return request({
    url: '/tcm/particle/price',
    method: 'post',
    data
  })
}
/*** 中药饮片列表 **/
export const sliceData = (data) => {
  return request({
    url: '/tcm/slice',
    method: 'get',
    params: data
  })
}
/** 获取生产企业 **/
export const gmpSelect = (data) => {
  return request({
    url: '/tcm/pe/select',
    method: 'get',
    params: data
  })
}
/** 修改生产企业 **/
export const setPe = (sliceId, peId) => {
  return request({
    url: `/tcm/slice/pe/${sliceId}/${peId}`,
    method: 'post'
  })
}
/** 中药饮片 设置价格 **/
export const setSlicePrice = (data) => {
  return request({
    url: '/tcm/slice/price',
    method: 'post',
    data
  })
}
/** 中药 获取别名 **/
export const aliasData = (sliceId) => {
  return request({
    url: '/tcm/slice/alias/' + sliceId,
    method: 'get'
  })
}
/** 中药 设置别名 **/
export const setAlias = (sliceId, data) => {
  return request({
    url: '/tcm/slice/alias/' + sliceId,
    method: 'put',
    data
  })
}
/** 添加中药 **/
export const setCnMedicine = (data) => {
  let method = 'post'
  if (data.id) {
    method = 'put'
  }
  return request({
    url: '/tcm/slice',
    method: method,
    data
  })
}
/** 中药饮片详情 **/
export const sliceInfo = (id) => {
  return request({
    url: '/tcm/slice/' + id,
    method: 'get'
  })
}
/** 删除中药饮片 **/
export const delSlice = (id) => {
  return request({
    url: '/tcm/slice/' + id,
    method: 'delete'
  })
}
/** 下载倒入模版 ***/
export const exportExcel = (data) => {
  return request({
    url: 'url',
    method: 'get'
  })
}
/** 批量导入价格接口路径 **/
export function excelURL() {
  return process.env.VUE_APP_BASE_API + '/tcm/slice/price/upload'
}
