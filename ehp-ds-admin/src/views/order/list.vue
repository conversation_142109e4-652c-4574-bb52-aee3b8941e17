<template>
  <div class="app-container">
    <!-- Tab 切换 -->
    <el-tabs
      v-show="!detailVisible && !sendgoodVisible && !mergeVisible && !receiveVisible"
      v-model="activeTab"
      style="margin-bottom: 20px;"
      @tab-click="handleTabChange"
    >
      <el-tab-pane label="全部订单" name="all"></el-tab-pane>
      <el-tab-pane label="待发货订单" name="pending"></el-tab-pane>
    </el-tabs>

    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.orderSn"
        clearable
        placeholder="订单号"
        style="width: 150px"
        type="textarea"
        :rows="4"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="收货手机号"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.receiver"
        clearable
        placeholder="收货人姓名"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        ref="dateStartPickerRef"
        :query-model="listQuery"
        style="width: 250px"
        start-placeholder="下单开始时间"
        end-placeholder="下单结束时间"
        @change="handleFilter"
      />
      <DatePicker
        ref="dateEndPickerRef"
        :query-model="listQuery"
        gte="sendTimeGte"
        lte="sendTimeLte"
        style="width: 250px"
        start-placeholder="发货开始时间"
        end-placeholder="发货结束时间"
        @change="handleFilter"
      />
      <DictSelect
        v-show="activeTab === 'all'"
        v-model="listQuery.orderStatus"
        placeholder="订单状态"
        style="width: 150px;"
        type="order_status"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.skuNumber"
        clearable
        placeholder="商品sku码"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.skuName"
        clearable
        placeholder="商品名称"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
    </div>
    <div class="filter-container">
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
      <el-button
        v-waves
        v-permission="['order:confirm']"
        type="primary"
        :disabled="sureOrderSelect.length === 0 ? true : false"
        @click="batchSure"
      >批量确认</el-button>
      <el-button
        v-waves
        v-permission="['order:confirm']"
        type="primary"
        :disabled="sureOrderSelect.length === 0 ? true : false"
        @click="batchRefuse"
      >批量拒绝</el-button>
      <el-button v-waves v-permission="['order']" type="primary" style="position:relative;">
        <template>
          <input
            id="imgfile"
            class="imgfile"
            type="file"
            multiple="false"
            accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            style="position:absolute;left:-9999px;top:0;"
            @change="handleFileChange($event)"
          />批量发货
          <label for="imgfile" style="position:absolute;left:0;top:0;bottom:0;right:0"></label>
        </template>
      </el-button>
      <el-button v-waves v-permission="['order']" type="primary" @click="downExcel">批量发货模板下载</el-button>
      <el-button v-waves v-permission="['order:export']" type="primary" @click="orderExport">订单导出</el-button>
      <el-button v-waves type="primary" @click="handleReset">重置</el-button>
      <el-button v-waves type="primary" @click="goMerge">合并发货</el-button>
    </div>
    <el-table
      v-if="!detailVisible && !sendgoodVisible && !mergeVisible && !receiveVisible"
      ref="goodsTable"
      :key="tableKey"
      :data="goodsList"
      border
      fit
      highlight-current-row
      @selection-change="goodsSelectionChange"
    >
      <el-table-column type="selection" align="center" width="50"></el-table-column>
      <el-table-column label="操作" align="center" width="200px">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.orderStatus===9"
            v-permission="['order:confirm']"
            type="text"
            @click="refuseSend(scope.row.id,0)"
          >订单确认</el-button>
          <el-button
            v-if="scope.row.orderStatus===9"
            v-permission="['order:confirm']"
            type="text"
            @click="refuseSend(scope.row.id,1)"
          >订单拒绝</el-button>
          <el-button
            v-if="scope.row.orderStatus===10"
            v-permission="['order:delivery']"
            type="text"
            @click="goOrderSure(scope.row.id, 1)"
          >订单发货</el-button>
          <el-button
            v-permission="['order:list']"
            type="text"
            @click="goOrderDetail(scope.row.id)"
          >订单查看</el-button>
          <el-button
            v-permission="['order:print']"
            type="text"
            @click="showPrint(scope.row.id)"
          >打印面单</el-button>
          <el-button
            v-permission="['order:print']"
            type="text"
            @click="showPrint(scope.row.id, '1')"
          >打印小票</el-button>
          <el-button
            v-if="scope.row.orderStatus == 4"
            type="text"
            @click="goInvoice(scope.row.orderSn, scope.row.invoice)"
          >{{ scope.row.invoice ? '编辑发票' : '上传发票' }}</el-button>
          <el-button
            v-if="scope.row.orderStatus===3"
            v-permission="['order:delivery']"
            type="text"
            @click="goOrderSure(scope.row.id, 2)"
          >物流变更</el-button>
          <el-button
            v-if="scope.row.orderStatus===3 || scope.row.orderStatus===10"
            v-permission="['order:delivery']"
            type="text"
            @click="goReceive(scope.row.id)"
          >收货信息变更</el-button>
        </template>
      </el-table-column>
      <el-table-column label="订单编号" prop="orderSn" align="center" width="170" />
      <el-table-column label="下单时间" prop="createdAt" align="center" width="160" />
      <el-table-column label="收货手机" prop="phone" align="center" width="120" />
      <el-table-column label="订单状态" prop="orderStatusDescribe" align="center" width="120" />
      <el-table-column label="订单应付金额" prop="totalMoney" align="center" width="110" />
      <el-table-column label="订单实付金额" prop="realPay" align="center" width="110" />
      <el-table-column label="收件人姓名" prop="receiver" align="center" width="100" />
      <el-table-column label="处方类型" prop="drugType" align="center" width="100">
        <template slot-scope="{row}">
          <el-tag v-if="row.drugType === 1">西药</el-tag>
          <el-tag v-if="row.drugType === 2">中药</el-tag>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog title="打印面单" :visible.sync="dialogPrintVisible">
      <div ref="printBox">
        <div>
          <div style="text-align:right;">
            <img src="/logo/ic_company.png" width="80px" style="float:left;" alt />
            <img :src="printData.orderSnBarcode" alt />
          </div>
          <el-row style="margin:15px 0;color:#000;">
            <el-col :span="12">
              <div class="grid-content bg-purple">收货人：{{ printData.receiver }}</div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple">订购日期：{{ printData.createdAt }}</div>
            </el-col>
          </el-row>
          <el-row style="margin:15px 0;color:#000;">
            <el-col :span="12">
              <div class="grid-content bg-purple">付款方式：{{ printData.payType }}</div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple"></div>
            </el-col>
          </el-row>
          <el-row style="margin:15px 0;color:#000;">
            <el-col :span="12">
              <div class="grid-content bg-purple">收货地址：{{ printData.address }}</div>
            </el-col>
            <el-col :span="12">
              <div class="grid-content bg-purple"></div>
            </el-col>
          </el-row>
        </div>
        <table
          class="printtable"
          width="100%"
          cellpadding="0"
          cellspacing="0"
          border="1px solid #ccc"
        >
          <tr>
            <th>商品编号</th>
            <template v-if="printData.recipeCategory===4">
              <th>名称</th>
              <th>价格</th>
              <th>数量</th>
              <th>总价</th>
            </template>
            <template v-else>
              <th>商品名</th>
              <th>单价</th>
              <th>数量</th>
              <th>金额</th>
            </template>
          </tr>
          <tr v-for="(item,index) in printData.orderItems" :key="index">
            <td>{{ item.productId }}</td>
            <td>{{ item.skuName }}</td>
            <td>{{ item.salePrice }}</td>
            <td>{{ item.quantity }}</td>
            <td v-if="printData.recipeCategory !== 4">{{ item.totalPrice }}</td>
            <td v-if="printData.recipeCategory === 4 && index === 0" :rowspan="printData.orderItems.length">{{ printData.totalAmount }}</td>
          </tr>
        </table>
      </div>
      <div style="text-align:center;margin-top:20px;">
        <el-button v-waves type="primary" @click="print">打印</el-button>
      </div>
    </el-dialog>

    <el-dialog title="打印小票" width="350px" :visible.sync="dialogPrintVisibleBill">
      <div id="receipt" ref="receipt">
        <div>
          <div class="title">{{ printData['warehouseName'] }}</div>
          <!-- <barcode
            style="margin:auto;display:block;"
            :value="printData['orderSnBarcode']"
            :display-value="false"
            width="1"
            height="30"
            font-size="14"
          /> -->
          <!-- <barcode class="barcode print_center" tag="img" :value="printData['orderSnBarcode']" width="1" height="20" font-size="14" /> -->
          <!-- <img :src="printData.orderSnBarcode" alt /> -->
          <div style="border: 1px dashed #979797;margin:20px 0;width: 100%;"></div>
          <el-row style="margin-bottom:5px;">
            <el-col :span="24">
              <div>订单编号：{{ printData.orderSn }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-bottom:5px;">
            <el-col :span="24">
              <div>
                <span>收件人：</span>
                <div>{{ printData.receiver }}</div></div>
            </el-col>
          </el-row>
          <el-row style="margin-bottom:5px;">
            <el-col :span="24">
              <div>联系电话：{{ printData.phone }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-bottom:5px;">
            <el-col :span="24">
              <div>下单时间：{{ printData.createdAt }}</div>
            </el-col>
          </el-row>
          <el-row style="margin-bottom:5px;">
            <el-col :span="24">
              <div style="display:flex;">
                <span style="white-space:nowrap;">收货地址：</span>
                <div>{{ printData.address }}</div></div>
            </el-col>
          </el-row>
        </div>
        <div style="border: 1px dashed #979797;margin:20px 0;width: 100%;"></div>
        <table style="width: 100%;">
          <thead>
            <tr>
              <td align="left" style="width: 50%;">菜单名称</td>
              <td style="width: 25%;">数量</td>
              <td style="width: 25%;">金额</td>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item,index) in printData.orderItems" :key="index">
              <td align="left">{{ item.skuName }}</td>
              <td>{{ item.salePrice }}</td>
              <td>{{ item.quantity }}</td>
            </tr>
          </tbody>
        </table>
        <div style="border: 1px dashed #979797;margin:10px 0;width: 100%;"></div>
        <div class="total" style="display: flex;">
          <div style="width: 50%;" class="left">合计</div>
          <div style="width: 25%;" class="center">{{ printData.totalQuantity }}</div>
          <div style="width: 25%;" class="right">{{ printData.totalAmount }}</div>
        </div>
        <div style="margin-top:12px">药品属于特殊商品，非 质量问题概不退换！</div>
      </div>
      <div style="text-align:center;margin-top:20px;">
        <el-button v-waves type="primary" @click="printPage">打印</el-button>
      </div>
    </el-dialog>

    <div id="receipt" ref="receipt" style="position:fixed;z-index:-9999;display:none;">
      <div>
        <div class="title print_f16" style="text-align:center;">{{ printData['warehouseName'] }}</div>
        <barcode style="text-align:center;display:block;" tag="img" :value="printData['orderSn']" width="1" height="20" font-size="14" />
        <div style="border-bottom: 1px dashed #979797;margin:20px 0;width: 100%;"></div>
        <el-row style="margin-bottom:5px;">
          <el-col :span="24">
            <div>订单编号：{{ printData.orderSn }}</div>
          </el-col>
        </el-row>
        <el-row style="margin-bottom:5px;">
          <el-col :span="24">
            <div style="display:flex;">
              <span>收件人：</span>
              <div>{{ printData.receiver }}</div>
            </div>
          </el-col>
        </el-row>
        <el-row style="margin-bottom:5px;">
          <el-col :span="24">
            <div>联系电话：{{ printData.phone }}</div>
          </el-col>
        </el-row>
        <el-row style="margin-bottom:5px;">
          <el-col :span="24">
            <div>下单时间：{{ printData.createdAt }}</div>
          </el-col>
        </el-row>
        <el-row style="margin-bottom:5px;">
          <el-col :span="24">
            <div style="display:flex;">
              <span style="white-space:nowrap;">收货地址：</span>
              <div>{{ printData.address }}</div></div>
          </el-col>
        </el-row>
      </div>
      <div style="border-bottom: 1px dashed #979797;margin:20px 0;width: 100%;"></div>
      <table style="width: 100%;">
        <thead>
          <tr>
            <td align="left" style="width: 60%;">商品名</td>
            <td>数量</td>
            <!-- <td style="width: 25%;">金额</td> -->
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item,index) in printData.orderItems" :key="index">
            <td style="margin-bottom: 10px;" align="left">{{ item.skuName }}</td>
            <td>{{ item.quantity }}</td>
            <!-- <td>{{ item.salePrice }}</td> -->
          </tr>
        </tbody>
      </table>
      <div style="border-bottom: 1px dashed #979797;margin:10px 0;width: 100%;"></div>
      <div class="total" style="display: flex;">
        <div style="width: 60%;" class="left">合计</div>
        <!-- <div style="width: 25%;" class="center">{{ printData.totalQuantity }}</div> -->
        <div class="right">{{ printData.totalAmount }}</div>
      </div>
      <div style="margin-top:12px">药品属于特殊商品，非 质量问题概不退换！</div>
    </div>
    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="detailVisible" class="transition-box">
        <DetailPage ref="detail" @goback="onBack" />
      </div>
    </el-collapse-transition>
    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="sendgoodVisible" class="transition-box">
        <sendgoodPage v-show="sendgoodVisible" ref="sendgood" @goback="onBack" />
      </div>
    </el-collapse-transition>

    <!-- 上传/编辑发票 -->
    <el-dialog
      :title="invoiceTile"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-upload
        :action="invoiceUploadUrl"
        :headers="headers"
        :on-preview="handlePreview"
        :on-remove="handleRemove"
        multiple
        accept=".png, .jpg, .pdf"
        :limit="1"
        :on-exceed="handleExceed"
        :before-upload="handleBeforeUpload"
        :on-success="handleSuccess"
        :file-list="fileList"
      >
        <el-button size="small" type="primary">选择上传文件</el-button>
        <div slot="tip" class="el-upload__tip">支持扩展名：.pdf，.jpg，.png，且不超过500KB</div>
      </el-upload>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="fileList.length == 0 ? true : false" @click="confirmUpload">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 合并发货 -->
    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="mergeVisible" class="transition-box">
        <MergeShipment ref="merge" @goback="onBack" />
      </div>
    </el-collapse-transition>
    <!-- 收货信息变更 -->
    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="receiveVisible" class="transition-box">
        <ReceiveDetail ref="receive" @goback="onBack" />
      </div>
    </el-collapse-transition>
  </div>
</template>
<script>
import Vue from 'vue'
import VueBarcode from 'vue-barcode'
import { printPage } from '@/utils/print' // 导入模块
import { getToken, getTokenName } from '@/utils/auth'
import api_order from '@/api/order/index'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import DetailPage from './detail.vue'
import sendgoodPage from './sendgood.vue'
import ReceiveDetail from './receiveDetail.vue'
import waves from '@/directive/waves' // Waves directive

import { isWindows } from '@/utils/index'
import LodopAPI from '@/utils/LodopFuncs' // 导入模块

import MergeShipment from './merge.vue'
export default {
  name: 'Orderlist',

  components: {
    DictSelect,
    DatePicker,
    DetailPage,
    sendgoodPage,
    barcode: VueBarcode,
    MergeShipment,
    ReceiveDetail
  },

  directives: { waves },

  data() {
    return {
      tableKey: 0,
      printData: {},
      dialogPrintVisible: false,
      dialogPrintVisibleBill: false,
      goodsList: [],
      sureOrderSelect: [],
      // statusData: ['', '待付款', '待审核', '订单审核完成', '库房分配完成', '药店已接收', '订单已发货', '订单已签收', '已取消'],
      total: 0,
      activeTab: 'all', // 当前激活的tab
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      detailVisible: false,
      sendgoodVisible: false,
      dialogVisible: false,
      invoiceTile: '上传发票',
      invoiceUploadUrl: '',
      headers: {},
      fileList: [],
      orderSn: '',
      mergeVisible: false,
      receiveVisible: false
    }
  },

  created() {
    this.headers[getTokenName()] = getToken()
    this.initTabStatus()
  },

  activated() {
    this.initTabStatus()
    this.getList()
  },
  methods: {
    downExcel() {
      window.location.href = '/static/excel/订单批量发货模板.xlsx'
    },
    // 初始化tab状态
    initTabStatus() {
      if (this.activeTab === 'pending') {
        this.listQuery.orderStatusList = '9,10'
      }
    },
    getList() {
      api_order.list(this.listQuery).then(response => {
        this.goodsList = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    // Tab切换处理
    handleTabChange(tab) {
      this.activeTab = tab.name
      this.listQuery.pageNo = 1

      // 根据tab设置订单状态
      if (tab.name === 'pending') {
        // 待发货订单，设置orderStatusList为9,10
        this.listQuery.orderStatusList = '9,10'
        delete this.listQuery.orderStatus
      } else {
        // 全部订单，清除orderStatusList过滤
        delete this.listQuery.orderStatusList
      }

      this.getList()
    },
    goOrderDetail(orderId) {
      this.$refs['detail'].getDetail(orderId)
      this.detailVisible = true
    },
    onBack() {
      this.detailVisible = false
      this.sendgoodVisible = false
      this.mergeVisible = false
      this.receiveVisible = false
      this.getList()
    },
    goOrderSure(orderId, type) {
      // type 1: 订单发货 2:物流变更
      this.$refs['sendgood'].orderId = orderId
      this.$refs['sendgood'].getDetail(orderId, type)
      this.$refs['sendgood'].getCompany()
      this.$refs['sendgood'].resetTemp()
      this.sendgoodVisible = true
    },
    goReceive(orderId) {
      this.$refs['receive'].getCityList()
      this.$refs['receive'].getDetail(orderId)
      this.receiveVisible = true
    },
    showPrint(orderId, type = '') {
      api_order.print(orderId).then(response => {
        const printData = response
        for (const i in printData.orderItems) {
          if (printData.drugType === 1) {
            printData.orderItems[i].salePrice = this.toFixedThree(printData.orderItems[i].salePrice / 100)
            printData.orderItems[i].totalPrice = this.toFixedThree(printData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            printData.orderItems[i].salePrice = this.toFixedThree(printData.orderItems[i].salePrice / 1000)
            printData.orderItems[i].totalPrice = this.toFixedThree(printData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        this.printData = printData
        if (type === '1') {
          // this.dialogPrintVisibleBill = true
          // this.renderDispensingData()
          this.printPage()
        } else {
          this.dialogPrintVisible = true
        }
      })
    },
    printPage() {
      if (isWindows()) {
        if (LodopAPI.getLodop()) {
          const LODOP = LodopAPI.getLodop()
          const Count = LODOP.GET_PRINTER_COUNT()
          console.log(Count, 'Count')
          this.$nextTick(() => {
            var strFormHtml = '<body>' + document.getElementById('receipt').innerHTML + '</body>'//获取打印内容
            LODOP.PRINT_INIT(this.printData.warehouseName) //初始化
            LODOP.SET_PRINTER_INDEXA('XP-58IIH') // 选择打印机
            LODOP.SET_PRINT_MODE('PRINT_PAGE_PERCENT', '100%')
            LODOP.SET_PRINT_PAGESIZE(1, '58mm', '', '') // 设置打印纸张宽度
            LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', strFormHtml) //设置打印内容
            LODOP.SET_PRINT_MODE('FULL_WIDTH_FOR_OVERFLOW', true)
            LODOP.SET_PRINT_MODE('FULL_HEIGHT_FOR_OVERFLOW', true)
            // LODOP.ADD_PRINT_BARCODE('20%', '30%', 140, 18, '128Auto', '123456789012')
            LODOP.PREVIEW()
            // LODOP.PRINT()
          })
        } else {
          this.$alert('<div>请安装打印机插件!<a style="margin-left: 10px;color: #7cd184;" href="https://cis-static-dev.oss-cn-shanghai.aliyuncs.com/abc-print/temp/CLodop_Setup_for_Win32NT.exe" target="view_window">点击下载</a></div>', '提示', {
            dangerouslyUseHTMLString: true
          })
        }
      } else {
        window.print()
      }
    },
    labelPrintMounted(printVm, params) {
      console.log(printVm, 'printVm')
      // const that = this
      // if (!(that.printer && that.printer.bill.cost.print !== '')) {
      //   Vue.prototype.$alert('请到管理，打印机设置，选择打印机！', '提示', {})
      //   this.printLoading && this.printLoading.close()
      //   return
      // }
      printPage({
        printIndex: '', // -1 默认打印机
        pageType: 'A5',
        PageWidth: 0,
        PageHeight: 0,
        orient: 1,
        count: params.printNum,
        elements: printVm.$el.querySelectorAll('.print-container'),
        title: this.printType
      })
    },
    // 打印发药单-80mm
    renderDispensingData() {
      const that = this
      const printVm = new Vue({
        components: {
          barcode: VueBarcode
        },
        data() {
          return {
            printType: that.printType,
            printData: that.printData
          }
        },
        mounted() {
          that.labelPrintMounted(printVm, { type: '80mm' })
        },
        render(h) {
          const printData = this.printData
          return (
            <div id='print' ref='receipt'>
              <div class='print-container'>
                <div style='text-align:center;font-size:9px;color:#333;font-weight:500;margin-bottom:10px;'>{ printData['warehouseName'] }</div>
                <barcode
                  style='margin:auto;display:block;'
                  value={ printData['orderSnBarcode'] }
                  width='1'
                  height='30'
                  font-size='14'
                />
                <div style='border: 1px dashed #979797;margin:20px 0;'></div>
                <el-row style='margin:9px 0;color:#000;'>
                  <el-col span={24}>
                    <div style='color:#333;font-size:7px'>订单编号：{ printData.orderSn }</div>
                  </el-col>
                </el-row>
                <el-row style='margin:9px 0;color:#000;'>
                  <el-col span={24}>
                    <div style='display:flex;'>
                      <span style='white-space:nowrap;color:#333;font-size:7px'>收件人：</span>
                      <div style='margin-left:10px;color:#333;font-size:7px'>{ printData.receiver }</div>
                    </div>
                  </el-col>
                </el-row>
                <el-row style='margin:9px 0;color:#000;'>
                  <el-col span={24}>
                    <div style='color:#333;font-size:7px'>联系电话：{ printData.phone }</div>
                  </el-col>
                </el-row>
                <el-row style='margin:9px 0;color:#000;'>
                  <el-col span={24}>
                    <div style='color:#333;font-size:7px'>下单时间：{ printData.createdAt }</div>
                  </el-col>
                </el-row>
                <el-row style='margin:9px 0;color:#000;'>
                  <el-col span={24}>
                    <div style='display:flex;'>
                      <span style='white-space:nowrap;color:#333;font-size:7px'>收货地址：</span>
                      <div style='margin-left:10px;color:#333;font-size:7px'>{ printData.address }</div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div style='border: 1px dashed #979797;margin:20px 0;'></div>
              <table style='width: 100%;'>
                <thead>
                  <tr>
                    <td style='font-size:7px' align='left' width='50%'>菜单名称</td>
                    <td style='font-size:7px' width='25%'>数量</td>
                    <td style='font-size:7px' width='25%'>金额</td>
                  </tr>
                </thead>
                <tbody>
                  {
                    this.printData.orderItems.map((item, index) => {
                      return (
                        <tr>
                          <td style='font-size:7px' align='left' width='50%'>{item.productName}</td>
                          <td style='font-size:7px' width='25%'>{item.salePrice}</td>
                          <td style='font-size:7px' width='25%'>{item.quantity}</td>
                        </tr>
                      )
                    })
                  }
                </tbody>
              </table>
              <div style='border: 1px dashed #979797;margin:20px 0;'></div>
              <div class='total'>
                <label style='font-size:7px;width:60%' class='left'>合计</label>
                <label style='font-size:7px;width:60%' class='center'>2</label>
                <label style='font-size:7px;width:60%' class='right'>39</label>
              </div>
              <div style='font-size:10px'>药品属于特殊商品，非 质量问题概不退换！</div>
            </div>
          )
        }
      })
      printVm.$mount('#print')
    },
    //(例子：0.1 --> 0.100)
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    },
    print() {
      this.$print(this.$refs.printBox)
    },
    orderExport() {
      const queryExport = this.listQuery
      delete queryExport.pageNo
      delete queryExport.pageSize
      queryExport[getTokenName()] = getToken()
      const url = api_order.orderexport()
      window.location.href = url + this.urlEncode(queryExport)
    },
    handleFileChange(event) {
      if (!event.target.files[0]) {
        return
      }
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      api_order.outGoods(param).then(
        response => {
          console.log(response)
          this.getList()
        },
        error => {
          // this.$refs['imgfile'].value = ''
          // this.$message({
          //   message: '批量发货失败！',
          //   type: 'error'
          // })
          console.log(error)
        }
      )
      event.target.value = null
      // this.$set(this.skuData,'images',images)
      console.log('上传')
    },
    batchSure() {
      this.$confirm('是否批量确认订单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_order.confirm(0, this.sureOrderSelect).then(response => {
          this.$message({
            message: '批量确认成功',
            type: 'success'
          })
          this.getList()
        })
      })
    },
    batchRefuse() {
      this.$confirm('是否批量拒绝订单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_order.confirm(1, this.sureOrderSelect).then(response => {
          this.$message({
            message: '批量拒绝成功',
            type: 'success'
          })
          this.getList()
        })
      })
    },
    refuseSend(orderId, status) {
      let statusStr
      if (status === 1) {
        statusStr = '是否拒绝订单'
      } else {
        statusStr = '是否确认订单'
      }
      this.$confirm(statusStr, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_order.confirm(status, [orderId]).then(response => {
          this.$message({
            message: '处理成功',
            type: 'success'
          })
          this.getList()
        })
      })
    },
    goodsSelectionChange(value) {
      if (value.length > 0) {
        var selectarr = []
        value.forEach((currentValue, index, arr) => {
          if (currentValue.orderStatus === 9) {
            selectarr.push(currentValue.id)
          }
        })
        this.sureOrderSelect = selectarr
      } else {
        this.sureOrderSelect = []
      }
    },
    urlEncode(param, key, encode) {
      if (param === null) return ''
      var paramStr = ''
      var t = typeof param
      if (t === 'string' || t === 'number' || t === 'boolean') {
        paramStr +=
          '&' +
          key +
          '=' +
          (encode === null || encode ? encodeURIComponent(param) : param)
      } else {
        for (var i in param) {
          var k =
            key === undefined
              ? i
              : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
          paramStr += this.urlEncode(param[i], k, encode)
        }
      }
      return paramStr
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10
      }

      // 重置时根据当前tab重新设置orderStatusList
      if (this.activeTab === 'pending') {
        this.listQuery.orderStatusList = '9,10'
      }

      this.handleFilter()
      this.$refs.dateStartPickerRef.reset()
      this.$refs.dateEndPickerRef.reset()
    },
    // 上传发票
    goInvoice(id, invoice) {
      this.invoiceTile = invoice ? '编辑发票' : '上传发票'
      this.orderSn = id
      this.fileList = invoice ? [{ name: invoice.filename, url: invoice.fileUrl }] : []
      this.invoiceUploadUrl = process.env.VUE_APP_BASE_API + '/order/invoice/upload/' + id
      this.dialogVisible = true
    },
    handleRemove(file, fileList) {
      console.log(file, fileList)
      this.fileList = []
    },
    handlePreview(file) {
      console.log(file)
      window.open(file.url)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`只能上传1个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`)
    },
    handleBeforeUpload(file) {
      const isLt = file.size / 1024 / 1024 < 0.5
      if (!isLt) {
        this.$message({
          message: '上传文件大小不能超过 500KB!',
          type: 'warning'
        })
      }
      return isLt
    },
    // 上传成功
    handleSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        this.fileList = fileList.map(file => {
          return {
            name: file.name,
            url: file.response.data
          }
        })
      }
    },
    // 保存发票
    confirmUpload() {
      if (Number(this.fileList.length) === 0) return
      const params = {
        fileUrl: this.fileList[0].url,
        orderSn: this.orderSn
      }
      api_order.saveInvoice(params).then(res => {
        this.$message({ message: '保存成功', type: 'success' })
        this.dialogVisible = false
        this.getList()
      })
    },
    // 合并发货
    goMerge() {
      this.$refs['merge'].getList()
      this.mergeVisible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container > div {
  float: left;
  margin-bottom: 10px;
  margin-right: 5px;
}
.printtable {
  border-collapse: collapse;
}
.printtable td,
.printtable th {
  padding: 10px;
  text-align: center;
  border: 1px solid #000;
}
.btn {
  position: relative;
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  color: #fff;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  float: none;
  margin-left: 10px;
  border: 1px solid #dcdfe6;
  background-color: #1890ff;
}
.transition-box{
  background: #fff;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 84px);
  overflow: auto;
}

.clearfix{
  clear: both;
}
.left{
  float: left;
}
.right{
  float:right;
}
.title {
  text-align: center;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333333;
}
.print_f16 {
  font-size: 16px;
}
::v-deep .el-upload-list__item.is-ready,
	::v-deep .el-upload-list__item.is-uploading {
    display: none !important;
	}
</style>
