<template>
  <div class="app-container">
    <div v-if="!orderDetailVisible" class="filter-container">
      <el-button v-waves type="primary" @click="goBack()">返回</el-button>
    </div>
    <el-table
      v-if="!orderDetailVisible"
      :data="list" 
      :key="tableKey" 
      fit
      empty-text="暂无可合并发货的订单"
      highlight-current-row 
      :span-method="spanMethod" 
      border 
      style="width: 100%; margin-top: 20px;"
      >
      <el-table-column label="收货信息" width="300">
        <template slot-scope="scope">
          <div>收货人：{{ scope.row.receiver }}</div>
          <div>手机：{{ scope.row.phone }}</div>
          <div>地址：{{ scope.row.fullAddress }}</div>
          <el-button style="width:100%;margin-top:20px" type="primary" @click="goSend(scope.row)">去合并发货</el-button>
        </template>
      </el-table-column>
      <el-table-column label="订单信息" width="250">
        <template slot-scope="scope">
          <div style="margin-bottom: 20px;">订单编号：{{ scope.row.orderSn }}</div>
          <div>订单时间：{{ scope.row.createdAt }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="orderStatusDescribe" label="订单状态" width="150"></el-table-column>
      <el-table-column label="商品信息" width="350">
        <template slot-scope="scope">
          <div style="margin-bottom: 20px;" v-for="good in scope.row.items" :key="good.name">{{ good.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数量" width="150">
        <template slot-scope="scope">
          <div style="margin-bottom: 20px;" v-for="good in scope.row.items" :key="good.name">{{ good.quantity }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="goOrderDetail(scope.row.orderId)">查看</el-button>
          <el-button type="text" style="color: red;" @click="remove(scope.row)">移出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="orderDetailVisible" class="transition-box">
        <!-- <Detail :order-data="orderData" /> -->
        <DetailPage ref="detail" @goback="onBack" />
      </div>
    </el-collapse-transition>

    <el-dialog
      title="合并发货"
      :visible.sync="sendVisible"
      width="500px"
      @close="onClose"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="paramsData"
        :rules="rules"
        label-position="right"
      >
        <el-form-item label="物流公司:" prop="expressId">
          <el-select style="width:350px;" v-model="paramsData.expressId" placeholder="请选择物流公司">
            <el-option
              v-for="item in companyData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号:" prop="deliveryId">
          <el-input
            v-model="paramsData.deliveryId"
            clearable
            style="width:350px;"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmOrder('ruleForm')">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Detail from './components/detail'
import DetailPage from './detail.vue'
import api_order from '@/api/order/index'
export default {
  name: 'Merge',
  directives: { waves },
  components: {
    Detail,
    DetailPage
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      orderData: {
        orderUser: {}
      },
      orderDetailVisible: false,
      sendVisible: false,
      paramsData: {},
      rules: {
        expressId: [{ required: true, message: '请选择物流公司', trigger: 'blur' }],
        //数字字母-
        deliveryId: [{ required: true, message: '请输入正确的物流单号,示例：德邦快递 DPK300548804377', trigger: 'blur', pattern: /^[0-9a-zA-Z\-]{1,}$/ }]
      },
      companyData: [],
      orderIds: []
    }
  },
  created() {},
  methods: {
    // 获取可合并订单列表
    getList() {
      api_order.getMergeOrderList()
        .then((response) => {
          console.log(response)
          const mergeList = response.map(item => {
            return {
              ...item,
              messageStr: `${item.receiver}-${item.phone}-${item.fullAddress}`
            }
          })
          const repeatArr = this.filterDuplicatesByMessageStr(mergeList, 'messageStr')
          console.log(repeatArr, 'repeatArr')
          if (repeatArr && repeatArr.length > 1) {
            this.list = repeatArr
          } else {
            this.list = []
          }
          // 排序
          this.list.sort((a, b) => a.messageStr.localeCompare(b.messageStr))
          console.log(this.list, 'this.list')
        })
        .catch((err) => {
          console.log(err)
        })
    },
    filterDuplicatesByMessageStr(list, field) {
      //数据
      const key = {} //存储的 key 是type的值，value是在indeces中对应数组的下标
      const indices = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同type的下标 
      list.map((item, index) => {
        //根据对应字段 分类（type）
        const type = item[field]
        const _index = key[type]
        if (_index !== undefined) {
          indices[_index].push(index)
        } else {
          key[type] = indices.length
          indices.push([index])
        }
      })
      // 归类结果
      const result = []
      indices.map((item) => {
        item.map((index) => {
          //result.push(List[index]) 相同项排序在一起
          //if (item.length > 1) {} 只要重复项
          //if (item.length == 1){} 只要单独项
          //我这里需要重复项 根据业务处理
          if (item.length > 1) {
            result.push(list[index])
          }
        })
      })
      //结果
      return result
    },
    hasDuplicateMessageStr(list) {
      const seen = new Set()
      for (const item of list) {
        if (seen.has(item.messageStr)) {
          // 如果messageStr已经在seen集合中，说明有重复，返回true
          return true
        }
        seen.add(item.messageStr) // 将当前item的messageStr添加到seen集合中
      }
      // 遍历结束，没有发现重复，返回false
      return false
    },
    getDetail(orderId) {
      api_order.detail(orderId).then(response => {
        const orderData = response
        for (const i in orderData.orderItems) {
          if (orderData.drugType === 1) {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 100)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 1000)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        this.orderData = orderData
      })
    },
    goOrderDetail(orderId) {
      this.$refs['detail'].getDetail(orderId)
      this.orderDetailVisible = true
    },
    getCompany() {
      api_order.company().then(response => {
        this.companyData = response
      })
    },
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 假设需要合并的是第一列
        let rowspan = 1
        // 判断是否是第一行或者当前行与上一行的收货人、手机号、地址信息不同
        if (
          rowIndex > 0 &&
          this.list[rowIndex - 1].messageStr === row.messageStr
        ) {
          // 如果信息相同，增加上一行的rowspan，当前单元格不显示
          rowspan = 0
        } else {
          // 信息不同，开始新的一组合并
          for (let i = rowIndex + 1; i < this.list.length; i++) {
            if (this.list[i].messageStr === row.messageStr) {
              rowspan++
            } else {
              break
            }
          }
        }
        return { rowspan, colspan: 1 }
      }
      return { rowspan: 1, colspan: 1 } // 其他列不合并
    },
    goBack() {
      this.$emit('goback')
    },
    onBack() {
      this.orderDetailVisible = false
    },
    goSend(row) {
      // 筛选符合条件的订单id
      this.orderIds = this.list.filter(ele => {
        return ele.messageStr === row.messageStr
      }).map(obj => obj.orderId)
      console.log(this.orderIds, 'this.orderIds')
      this.sendVisible = true
      this.getCompany()
    },
    remove(row) {
      this.list.splice(this.list.indexOf(row), 1)
    },
    onClose() {
      this.$refs['ruleForm'].resetFields()
    },
    confirmOrder(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            expressId: this.paramsData.expressId,
            deliveryId: this.paramsData.deliveryId
          }
          api_order.mergeOrders(this.orderIds, params.expressId, params.deliveryId).then(response => {
            this.$message({
              message: '发货成功',
              type: 'success'
            })
            this.sendVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 隐藏伪元素（表格多出的横线）
// ::v-deep.el-table::before {
//   display: none !important;
// }
</style>
