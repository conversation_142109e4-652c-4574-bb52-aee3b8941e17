<template>
  <div class="app-container">
    <div v-if="!orderDetailVisible" class="filter-container">
      <el-button v-waves type="primary" @click="goBack()">返回</el-button>
    </div>
    <el-table
      v-if="!orderDetailVisible"
      :key="tableKey"
      :data="list"
      fit
      empty-text="暂无可合并发货的订单"
      highlight-current-row
      :span-method="spanMethod"
      border
      style="width: 100%; margin-top: 20px;"
    >
      <el-table-column label="收货信息" width="300">
        <template slot-scope="scope">
          <div>收货人：{{ scope.row.receiver }}</div>
          <div>手机：{{ scope.row.phone }}</div>
          <div>地址：{{ scope.row.fullAddress }}</div>
          <el-button style="width:100%;margin-top:20px" type="primary" @click="goSend(scope.row)">去合并发货</el-button>
        </template>
      </el-table-column>
      <el-table-column label="订单信息" width="250">
        <template slot-scope="scope">
          <div style="margin-bottom: 20px;">订单编号：{{ scope.row.orderSn }}</div>
          <div>订单时间：{{ scope.row.createdAt }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="orderStatusDescribe" label="订单状态" width="150"></el-table-column>
      <el-table-column label="商品信息" width="350">
        <template slot-scope="scope">
          <div v-for="good in scope.row.items" :key="good.name" style="margin-bottom: 20px;">{{ good.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数量" width="150">
        <template slot-scope="scope">
          <div v-for="good in scope.row.items" :key="good.name" style="margin-bottom: 20px;">{{ good.quantity }}</div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="goOrderDetail(scope.row.orderId)">查看</el-button>
          <el-button type="text" style="color: red;" @click="remove(scope.row)">移出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-collapse-transition name="el-zoom-in-top">
      <div v-show="orderDetailVisible" class="transition-box">
        <!-- <Detail :order-data="orderData" /> -->
        <DetailPage ref="detail" @goback="onBack" />
      </div>
    </el-collapse-transition>

    <el-dialog
      title="合并发货"
      :visible.sync="sendVisible"
      width="500px"
      @close="onClose"
    >
      <el-form
        ref="ruleForm"
        :inline="true"
        :model="paramsData"
        :rules="rules"
        label-position="right"
      >
        <el-form-item label="物流公司:" prop="expressId">
          <el-select v-model="paramsData.expressId" style="width:350px;" placeholder="请选择物流公司">
            <el-option
              v-for="item in companyData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号:" prop="deliveryId">
          <el-input
            v-model="paramsData.deliveryId"
            clearable
            style="width:350px;"
          />
        </el-form-item>

        <!-- 发货凭证部分 -->
        <el-form-item :label="`发货凭证(${invoiceFileList.length}/4):`" prop="invoiceFiles">
          <div class="invoice-upload-container">
            <div class="upload-buttons">
              <el-button type="primary" icon="el-icon-upload2" @click="handleLocalUpload">本地上传</el-button>
              <el-button type="primary" icon="el-icon-camera" @click="handleQrUpload">扫码上传</el-button>
              <div class="upload-tips-text">
                <div class="tip-line">上传发货照片凭证留底，包含打印面单/小票+商品、发货单+包裹</div>
                <div class="tip-line">最多上传4张图，支持.png；.jpeg；.jpg文件且小于4MB</div>
              </div>
            </div>

            <!-- 文件上传组件 -->
            <el-upload
              ref="invoiceUpload"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :file-list="invoiceFileList"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="handleRemoveFile"
              :before-upload="beforeUpload"
              :limit="4"
              :on-exceed="handleExceed"
              accept=".png,.jpg,.jpeg"
              list-type="picture-card"
              :auto-upload="true"
              name="files"
              style="display: none;"
            >
            </el-upload>

            <!-- 图片预览区域 -->
            <div v-if="invoiceFileList.length > 0" class="image-preview-container">
              <div v-for="(file, index) in invoiceFileList" :key="index" class="image-preview-item">
                <img :src="file.url" :alt="file.name" @click="previewImage(file.url)" />
                <div class="image-actions">
                  <i class="el-icon-zoom-in" @click="previewImage(file.url)"></i>
                  <i class="el-icon-delete" @click="removeImage(index)"></i>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="sendVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmOrder('ruleForm')">确 认</el-button>
      </span>
    </el-dialog>

    <!-- 二维码弹窗 -->
    <el-dialog
      title="扫码上传发货凭证"
      :visible.sync="qrDialogVisible"
      width="400px"
      center
      @close="handleQrDialogClose"
    >
      <div class="qr-container">
        <div class="qr-code-area">
          <canvas ref="qrCanvas" width="200" height="200"></canvas>
          <div v-if="!qrCodeUrl" class="qr-loading">
            <i class="el-icon-loading"></i>
            <span>正在生成二维码...</span>
          </div>
        </div>
        <p class="qr-tips">请使用手机扫描二维码进行图片上传</p>
      </div>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog :visible.sync="previewDialogVisible" width="800px" center>
      <img :src="previewImageUrl" style="width: 100%;" alt="预览图片" />
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import DetailPage from './detail.vue'
import api_order from '@/api/order/index'
import { getToken } from '@/utils/auth'
import QRCode from 'qrcode'
export default {
  name: 'Merge',

  components: {
    DetailPage
  },

  data() {
    return {
      tableKey: 0,
      list: [],
      orderData: {
        orderUser: {}
      },
      orderDetailVisible: false,
      sendVisible: false,
      paramsData: {},
      rules: {
        expressId: [{ required: true, message: '请选择物流公司', trigger: 'blur' }],
        deliveryId: [{ required: true, message: '请输入正确的物流单号,示例：德邦快递 DPK300548804377', trigger: 'blur', pattern: /^[0-9a-zA-Z\-]{1,}$/ }]
      },
      companyData: [],
      orderIds: [],
      invoiceFileList: [],
      uploadUrl: '',
      uploadHeaders: {},
      qrDialogVisible: false,
      qrCodeUrl: '',
      previewDialogVisible: false,
      previewImageUrl: '',
      pollingInterval: null,
      uploadUuid: ''
    };
  },

  directives: { waves },
  created() {},

  methods: {
    // 获取可合并订单列表
    getList() {
      api_order.getMergeOrderList()
        .then((response) => {
          console.log(response)
          const mergeList = response.map(item => {
            return {
              ...item,
              messageStr: `${item.receiver}-${item.phone}-${item.fullAddress}`
            }
          })
          const repeatArr = this.filterDuplicatesByMessageStr(mergeList, 'messageStr')
          console.log(repeatArr, 'repeatArr')
          if (repeatArr && repeatArr.length > 1) {
            this.list = repeatArr
          } else {
            this.list = []
          }
          // 排序
          this.list.sort((a, b) => a.messageStr.localeCompare(b.messageStr))
          console.log(this.list, 'this.list')
        })
        .catch((err) => {
          console.log(err)
        })
    },
    filterDuplicatesByMessageStr(list, field) {
      //数据
      const key = {} //存储的 key 是type的值，value是在indeces中对应数组的下标
      const indices = [] //数组中每一个值是一个数组，数组中的每一个元素是原数组中相同type的下标
      list.map((item, index) => {
        //根据对应字段 分类（type）
        const type = item[field]
        const _index = key[type]
        if (_index !== undefined) {
          indices[_index].push(index)
        } else {
          key[type] = indices.length
          indices.push([index])
        }
      })
      // 归类结果
      const result = []
      indices.map((item) => {
        item.map((index) => {
          //result.push(List[index]) 相同项排序在一起
          //if (item.length > 1) {} 只要重复项
          //if (item.length == 1){} 只要单独项
          //我这里需要重复项 根据业务处理
          if (item.length > 1) {
            result.push(list[index])
          }
        })
      })
      //结果
      return result
    },
    hasDuplicateMessageStr(list) {
      const seen = new Set()
      for (const item of list) {
        if (seen.has(item.messageStr)) {
          // 如果messageStr已经在seen集合中，说明有重复，返回true
          return true
        }
        seen.add(item.messageStr) // 将当前item的messageStr添加到seen集合中
      }
      // 遍历结束，没有发现重复，返回false
      return false
    },
    getDetail(orderId) {
      api_order.detail(orderId).then(response => {
        const orderData = response
        for (const i in orderData.orderItems) {
          if (orderData.drugType === 1) {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 100)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 1000)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        this.orderData = orderData
      })
    },
    goOrderDetail(orderId) {
      this.$refs['detail'].getDetail(orderId)
      this.orderDetailVisible = true
    },
    getCompany() {
      api_order.company().then(response => {
        this.companyData = response
      })
    },
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    },
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 假设需要合并的是第一列
        let rowspan = 1
        // 判断是否是第一行或者当前行与上一行的收货人、手机号、地址信息不同
        if (
          rowIndex > 0 &&
          this.list[rowIndex - 1].messageStr === row.messageStr
        ) {
          // 如果信息相同，增加上一行的rowspan，当前单元格不显示
          rowspan = 0
        } else {
          // 信息不同，开始新的一组合并
          for (let i = rowIndex + 1; i < this.list.length; i++) {
            if (this.list[i].messageStr === row.messageStr) {
              rowspan++
            } else {
              break
            }
          }
        }
        return { rowspan, colspan: 1 }
      }
      return { rowspan: 1, colspan: 1 } // 其他列不合并
    },
    goBack() {
      this.$emit('goback')
    },
    onBack() {
      this.orderDetailVisible = false
    },
    goSend(row) {
      // 筛选符合条件的订单id
      this.orderIds = this.list.filter(ele => {
        return ele.messageStr === row.messageStr
      }).map(obj => obj.orderId)
      console.log(this.orderIds, 'this.orderIds')
      this.sendVisible = true
      this.getCompany()
    },
    remove(row) {
      this.list.splice(this.list.indexOf(row), 1)
    },
    onClose() {
      this.$refs['ruleForm'].resetFields()
      this.invoiceFileList = []
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }
    },
    confirmOrder(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            expressId: this.paramsData.expressId,
            deliveryId: this.paramsData.deliveryId
          }
          api_order.mergeOrders(this.orderIds, params.expressId, params.deliveryId).then(response => {
            this.$message({
              message: '发货成功',
              type: 'success'
            })
            this.sendVisible = false
            this.getList()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async handleLocalUpload() {
      try {
        await this.getUploadUuid()
        this.configureUpload()
        this.$refs.invoiceUpload.$el.querySelector('input').click()
      } catch (error) {
        console.error('准备上传失败:', error)
        this.$message.error('准备上传失败，请重试')
      }
    },
    async getUploadUuid() {
      try {
        if (!this.orderIds || this.orderIds.length === 0) {
          throw new Error('订单ID不存在')
        }
        const response = await api_order.getUuid(this.orderIds[0])
        this.uploadUuid = response.uuid || response
        console.log('获取上传UUID成功:', this.uploadUuid)
      } catch (error) {
        console.error('获取上传UUID失败:', error)
        throw error
      }
    },
    configureUpload() {
      if (!this.uploadUuid) {
        throw new Error('上传UUID不存在')
      }
      this.uploadUrl = process.env.VUE_APP_BASE_API + `/order/shipping/img/upload/${this.uploadUuid}`
      this.uploadHeaders = {
        Authorization: getToken()
      }
      console.log('配置上传参数:', { uploadUrl: this.uploadUrl, uuid: this.uploadUuid })
    },
    async handleQrUpload() {
      try {
        if (!this.orderIds || this.orderIds.length === 0) {
          this.$message.error('订单ID不能为空')
          return
        }

        const token = getToken()
        if (!token) {
          this.$message.error('用户未登录，请重新登录')
          return
        }

        this.qrDialogVisible = true
        this.qrCodeUrl = ''

        await this.$nextTick()

        const canvas = this.$refs.qrCanvas
        if (!canvas) {
          console.error('Canvas元素未找到')
          this.$message.error('二维码容器未准备好，请重试')
          this.qrDialogVisible = false
          return
        }

        const ctx = canvas.getContext('2d')
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        await this.getUploadUuid()

        this.qrCodeUrl = `${window.location.origin}/upload-mobile?orderId=${this.orderIds[0]}&orderSn=merge_${this.orderIds.join('_')}&token=${token}&uuid=${this.uploadUuid}&isMerge=true`

        console.log('生成二维码URL:', this.qrCodeUrl)

        await QRCode.toCanvas(canvas, this.qrCodeUrl, {
          width: 200,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })

        console.log('二维码生成成功')

        this.startPollingUploadedFiles()
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$message.error(`生成二维码失败: ${error.message || '未知错误'}`)
        this.qrDialogVisible = false
      }
    },
    startPollingUploadedFiles() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
      }

      this.pollingInterval = setInterval(() => {
        this.checkUploadedFiles()
      }, 2000) // 改为2秒检查一次，与sendgood.vue保持一致
    },
    checkUploadedFiles() {
      try {
        // 🔥 简化：只检查扫码访问和上传成功标识
        const qrScanned = JSON.parse(localStorage.getItem('qrCodeScanned') || '{}')
        const hasQrScanned = this.orderIds.includes(qrScanned.orderId) &&
                            qrScanned.timestamp &&
                            typeof qrScanned.timestamp === 'number' &&
                            (Date.now() - qrScanned.timestamp < 60000) // 1分钟内的扫码访问

        const uploadSync = JSON.parse(localStorage.getItem('uploadSyncFlag') || '{}')
        const hasUploadSync = this.orderIds.includes(uploadSync.orderId) &&
                             uploadSync.uploadSuccess &&
                             uploadSync.timestamp &&
                             typeof uploadSync.timestamp === 'number' &&
                             (Date.now() - uploadSync.timestamp < 120000) // 2分钟内的上传成功

        // 🔥 如果检测到扫码访问，立即关闭弹框
        if (hasQrScanned && this.qrDialogVisible) {
          this.qrDialogVisible = false
          this.$message.success('检测到用户已扫码，正在准备上传页面...')
          localStorage.removeItem('qrCodeScanned')
          console.log('检测到扫码访问，已自动关闭二维码弹框')
        }

        // 🔥 如果检测到上传成功，刷新页面显示
        if (hasUploadSync) {
          console.log('检测到移动端上传成功')
          localStorage.removeItem('uploadSyncFlag')
          
          // 关闭弹框（如果还开着）
          if (this.qrDialogVisible) {
            this.qrDialogVisible = false
            this.$message.success('移动端上传成功！')
          }

          // 对于合并订单，我们暂时显示提示信息，因为合并订单可能没有直接的图片列表刷新接口
          this.$message.info('图片已上传成功，如需查看请在订单详情中确认')
        }
      } catch (error) {
        console.error('检查上传文件失败:', error)
      }
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isValidType = ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)
      const isLt4M = file.size / 1024 / 1024 < 4

      if (!isImage || !isValidType) {
        this.$message.error('只支持上传 .png、.jpg、.jpeg 格式的图片文件!')
        return false
      }

      if (!isLt4M) {
        this.$message.error('上传图片大小不能超过 4MB!')
        return false
      }

      if (this.invoiceFileList.length >= 4) {
        this.$message.error('最多只能上传4张图片!')
        return false
      }

      return true
    },
    handleUploadSuccess(response, file, fileList) {
      console.log('PC端上传响应:', response)
      console.log('PC端上传文件:', file)
      if (response.code === 0 || response.success) {
        this.$message.success('图片上传成功')

        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          console.log('上传成功的文件路径:', response.data.map(item => item.path))
        }

        this.$nextTick(() => {
          this.$refs.ruleForm.validateField('invoiceFiles')
        })
      } else {
        this.$message.error(response.msg || response.message || '上传失败')
      }
    },
    handleUploadError(error, file, fileList) {
      console.error('上传失败:', error)
      this.$message.error('图片上传失败，请重试')
    },
    handleRemoveFile(file, fileList) {
      const index = this.invoiceFileList.findIndex((item) => item.uid === file.uid)
      if (index > -1) {
        this.invoiceFileList.splice(index, 1)
        this.$nextTick(() => {
          this.$refs.ruleForm.validateField('invoiceFiles')
        })
      }
    },
    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传4张图片')
    },
    removeImage(index) {
      this.$confirm('确定要删除这张图片吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.invoiceFileList.splice(index, 1)
          this.$message.success('删除成功')

          this.$nextTick(() => {
            this.$refs.ruleForm.validateField('invoiceFiles')
          })
        })
        .catch(() => {
          // 取消删除
        })
    },
    previewImage(url) {
      this.previewImageUrl = url
      this.previewDialogVisible = true
    },
    handleQrDialogClose() {
      if (this.pollingInterval) {
        console.log('二维码弹框已关闭，将在5分钟后停止轮询同步')

        setTimeout(() => {
          if (this.pollingInterval) {
            clearInterval(this.pollingInterval)
            this.pollingInterval = null
            console.log('轮询同步已停止')
          }
        }, 300000) // 5分钟延迟 (300秒)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
// 隐藏伪元素（表格多出的横线）
// ::v-deep.el-table::before {
//   display: none !important;
// }

/* 发货凭证样式 */
.invoice-upload-container {
  width: 100%;
}

.upload-buttons {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.upload-buttons .el-button {
  margin-right: 15px;
}

.upload-tips-text {
  margin-left: 15px;
  flex: 1;
}

.tip-line {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  line-height: 1.4;
}

.tip-line:last-child {
  margin-bottom: 0;
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.image-preview-item {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fbfdff;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-item:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
}

.image-actions i:hover {
  transform: scale(1.2);
}

.qr-container {
  text-align: center;
  padding: 20px;
}

.qr-code-area {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

.qr-tips {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  color: #666;
}

.qr-loading i {
  font-size: 24px;
  margin-bottom: 10px;
  color: #409eff;
}
</style>