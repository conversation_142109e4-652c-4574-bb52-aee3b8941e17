<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        @click="goBack()"
      >返回</el-button>
    </div>
    <Detail :order-data="orderData" :img-list="invoiceImgList" />
  </div>
</template>
<style>

</style>
<script scoped>
import waves from '@/directive/waves'
import Detail from './components/detail'
import api_order from '@/api/order/index'
export default {
  'name': 'OrderDetail',
  directives: { waves },
  components: {
    Detail
  },
  data() {
    return {
      orderId: null,
      orderData: {
        orderUser: {}
      },
      invoiceImgList: []
    }
  },
  created() {
    // this.orderId = this.$route.params.orderId
    // this.getDetail()
  },
  methods: {
    getDetail(orderId) {
      api_order.detail(orderId).then(response => {
        const orderData = response
        for (const i in orderData.orderItems) {
          if (orderData.drugType === 1) {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 100)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 1000)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        this.orderData = orderData
        this.getInvoiceImgList()
      })
    },
    // 获取发货凭证列表
    getInvoiceImgList() {
      api_order.getInvoiceImgList(this.orderData.orderSn).then(res => {
        this.invoiceImgList = res || []
      })
    },
    goBack() {
      this.$emit('goback')
    },
    //(例子：0.1 --> 0.100)
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    }
  }
}
</script>
