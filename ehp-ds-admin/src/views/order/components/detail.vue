<template>
  <div>
    <div class="dtitle">基本信息</div>
    <table class="table" cellspacing="1" cellpadding="0" border="0">
      <tr>
        <td width="10%">订单号</td>
        <td width="35%">{{ orderData.orderSn }}</td>
        <td width="10%">订单状态</td>
        <td width="35%">{{ orderData.orderStatusDescribe }}</td>
      </tr>
      <tr>
        <td>用户名</td>
        <td>{{ orderData.userName }}</td>
        <td>下单时间</td>
        <td>{{ orderData.createdAt }}</td>
      </tr>
      <tr>
        <td>支付方式</td>
        <td>{{ orderData.payTypeDescribe }}</td>
        <td>付款时间</td>
        <td>{{ orderData.payTime }}</td>
      </tr>
      <tr>
        <td>配送方式</td>
        <td>{{ orderData.logisticsCompany }}</td>
        <td>发货时间</td>
        <td>{{ orderData.sendTime }}</td>
      </tr>
      <tr>
        <td>发货单号</td>
        <td>{{ orderData.deliveryId }}</td>
        <td>处方类型</td>
        <td>{{ orderData.drugType == 1 ? '西药' : '中药' }}</td>
      </tr>
      <tr>
        <td>发货凭证</td>
        <td style="display: flex;flex-wrap: wrap;">
          <div v-for="(item, index) in orderData.invoiceImgList" :key="index">
            <img :src="item.path" style="width: 100px;height: 100px;margin-right: 20px;" />
          </div>
        </td>
      </tr>
    </table>
    <div class="dtitle">发票信息</div>
    <table class="table" cellspacing="1" cellpadding="0" border="0">
      <tr>
        <td width="10%">发票抬头</td>
        <td width="35%">{{ orderData.invoiceTitle }}</td>
        <td width="10%">发票内容</td>
        <td width="35%">{{ orderData.invoiceContent }}</td>
      </tr>
      <tr>
        <td>用户备注</td>
        <td>{{ orderData.remark }}</td>
        <td>客服备注</td>
        <td></td>
      </tr>
    </table>
    <div v-if="receive" class="dtitle">收货人信息</div>
    <table v-if="receive" class="table" cellspacing="1" cellpadding="0" border="0">
      <tr>
        <td width="10%">收货人</td>
        <td width="35%">{{ orderData.orderUser.receiver }}</td>
        <td width="10%">手机</td>
        <td width="35%">{{ orderData.orderUser.phone }}</td>
      </tr>
      <tr>
        <td>地址</td>
        <td>{{ orderData.orderUser.fullAddress }}</td>
        <td>邮编</td>
        <td>{{ orderData.orderUser.zipcode }}</td>
      </tr>
      <tr>
        <td>客户留言</td>
        <td></td>
        <td></td>
        <td></td>
      </tr>
    </table>
    <div class="dtitle">商品信息</div>
    <el-table
      ref="goodsTable"
      :key="tableKey"
      :data="orderData.orderItems"
      border
      fit
      highlight-current-row
      style="margin-bottom: 10px;"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="商品名称" prop="productName" align="center" />
      <el-table-column label="商品sku码" prop="skuNumber" align="center" />
      <el-table-column label="商品规格" prop="skuName" align="center" />
      <el-table-column label="销售单价" prop="salePrice" align="center">
        <template slot-scope="scope">
          <span v-if="orderData.recipeCategory != 4">{{ scope.row.salePrice }}</span>
          <span v-else>0.000</span>
        </template>
      </el-table-column>
      <el-table-column label="实付单价" prop="salePrice" align="center">
        <template slot-scope="scope">
          <span v-if="orderData.recipeCategory != 4">{{ scope.row.salePrice }}</span>
          <span v-else>0.000</span>
        </template>
      </el-table-column>
      <el-table-column label="商品数量" prop="quantity" align="center" />
      <el-table-column label="合计实付" prop="totalPrice" align="center">
        <template slot-scope="scope">
          <span v-if="orderData.recipeCategory != 4">{{ scope.row.totalPrice }}</span>
          <span v-else>{{ orderData.totalAmount }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="dtitle">处方信息</div>
    <div class="imgbox" style="margin-bottom: 10px;">
      <!--viewer :images="orderData.prescriptionUrl">
        <img v-for="(item,index) in orderData.prescriptionUrl" :key="index" :src="item" width="300" alt="" />
      </viewer-->
      <iframe
        v-for="(item, index) in orderData.prescriptionUrl"
        :key="index"
        frameborder="0"
        name="showHere"
        width="700"
        height="500"
        scrolling="auto"
        :src="item"
      ></iframe>
    </div>
    <div class="dtitle">订单费用信息</div>
    <div class="imgbox">
      <div class="totalprice line">
        商品总金额：￥ <span class="blue">{{ orderData.totalAmount }}</span> 元 + 物流成本：￥
        <span class="blue">{{ orderData.freight }}</span> 元
      </div>
      <div class="totalprice">
        = 订单总金额：￥ <span class="red">{{ orderData.realPay }}</span> 元
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Detail',
  props: {
    orderData: {
      type: Object,
      default: function() {
        return {}
      }
    },
    receive: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableKey: '1'
    }
  },
  methods: {
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column, rowIndex, columnIndex, 'objectSpanMethod')
      if (columnIndex === 6 && this.orderData.recipeCategory === 4) {
        if (rowIndex === 0) {
          return {
            rowspan: 2,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    }
  }
}
</script>

<style scoped>
.dtitle {
  background-color: #f5f7fa;
  font-size: 14px;
  padding: 0 20px;
  font-weight: 500;
  line-height: 40px;
  border: 1px solid #dfe4ed;
  border-bottom: none;
}
.table {
  width: 100%;
  background-color: #dfe6ec;
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #909399;
}
.table tr td {
  background-color: #fff;
  padding: 10px;
}
.imgbox {
  border: 1px solid #dfe6ec;
}
.imgbox .line {
  border-bottom: 1px solid #dfe6ec;
}
.totalprice {
  padding: 0 10px;
  line-height: 40px;
  text-align: right;
  font-size: 14px;
  color: #909399;
}
.totalprice .red {
  color: #f00;
}
.totalprice .blue {
  color: blue;
}
</style>