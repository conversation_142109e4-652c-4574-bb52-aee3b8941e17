<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button v-waves type="primary" @click="goBack()">返回</el-button>
      <el-button
        v-if="type == 1"
        v-waves
        v-permission="['order:delivery']"
        type="primary"
        :disabled="orderData.orderStatus == 3 || orderData.orderStatus == 4"
        @click="sureSend('dataForm')"
      >确认发货</el-button>
      <el-button
        v-if="type == 2"
        v-waves
        v-permission="['order:delivery']"
        type="primary"
        :disabled="orderData.orderStatus !== 3"
        @click="sureSend('dataForm')"
      >确认变更</el-button>
    </div>
    <div class="dtitle">发货信息</div>
    <div class="imgbox" style="padding: 20px 20px 0; font-size: 14px; margin-bottom: 10px;">
      <el-form ref="dataForm" :inline="false" :model="paramsData" :rules="rules" label-position="right">
        <el-form-item label="物流公司:" prop="expressId">
          <el-select v-model="paramsData.expressId" placeholder="请选择物流公司">
            <el-option v-for="item in companyData" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号:" prop="deliveryId">
          <el-input
            v-model="paramsData.deliveryId"
            style="width: 200px;"
            clearable
            @keyup.enter.native="handleFilter"
          />
          <div style="margin-top: 10px; font-size: 12px; color: #909399;">
            支持扫码抢扫填录入单号，扫描后即可将单号自动录入至输入框中
          </div>
        </el-form-item>

        <!-- 发票凭证部分 -->
        <el-form-item :label="`发货凭证(${invoiceFileList.length}/4):`" prop="invoiceFiles">
          <div class="invoice-upload-container">
            <div class="upload-buttons">
              <el-button type="primary" icon="el-icon-upload2" @click="handleLocalUpload">本地上传</el-button>
              <el-button type="primary" icon="el-icon-camera" @click="handleQrUpload">扫码上传</el-button>
              <div class="upload-tips-text">
                <div class="tip-line">上传发货照片凭证留底，包含打印面单/小票+商品、发货单+包裹</div>
                <div class="tip-line">最多上传4张图，支持.png；.jpeg；.jpg文件且小于4MB</div>
              </div>
            </div>

            <!-- 文件上传组件 -->
            <el-upload
              ref="invoiceUpload"
              :action="uploadUrl"
              :headers="uploadHeaders"
              :file-list="invoiceFileList"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-remove="handleRemoveFile"
              :before-upload="beforeUpload"
              :limit="4"
              :on-exceed="handleExceed"
              accept=".png,.jpg,.jpeg"
              list-type="picture-card"
              :auto-upload="true"
              name="files"
              style="display: none;"
            >
            </el-upload>

            <!-- 图片预览区域 -->
            <div v-if="invoiceFileList.length > 0" class="image-preview-container">
              <div v-for="(file, index) in invoiceFileList" :key="index" class="image-preview-item">
                <img :src="file.url" :alt="file.name" @click="previewImage(file.url)" />
                <div class="image-actions">
                  <i class="el-icon-zoom-in" @click="previewImage(file.url)"></i>
                  <i class="el-icon-delete" @click="removeImage(index)"></i>
                </div>
              </div>
            </div>

            <!-- 二维码弹窗 -->
            <el-dialog
              title="扫码上传发货凭证"
              :visible.sync="qrDialogVisible"
              width="400px"
              center
              @close="handleQrDialogClose"
            >
              <div class="qr-container">
                <div class="qr-code-area">
                  <canvas ref="qrCanvas" width="200" height="200"></canvas>
                  <div v-if="!qrCodeUrl" class="qr-loading">
                    <i class="el-icon-loading"></i>
                    <span>正在生成二维码...</span>
                  </div>
                </div>
                <p class="qr-tips">请使用手机扫描二维码进行图片上传</p>
                <!-- <p v-if="qrCodeUrl" class="qr-url">{{ qrCodeUrl }}</p> -->
              </div>
            </el-dialog>

            <!-- 图片预览弹窗 -->
            <el-dialog :visible.sync="previewDialogVisible" width="80%" center>
              <img :src="previewImageUrl" style="width: 100%;max-height: 70vh; object-fit: contain;" alt="预览图片" />
            </el-dialog>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <Detail :order-data="orderData" :img-list="invoiceFileList" />
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Detail from './components/detail'
import api_order from '@/api/order/index'
import { getToken } from '@/utils/auth'
import QRCode from 'qrcode'
export default {
  name: 'OrderShipment',

  components: {
    Detail
  },

  directives: { waves },

  data() {
    return {
      paramsData: {},
      orderId: null,
      orderData: {
        orderUser: {}
      },
      companyData: [],
      rules: {
        expressId: [{ required: true, message: '请选择物流公司', trigger: 'blur' }],
        deliveryId: [
          {
            required: true,
            message: '请输入正确的物流单号,示例：德邦快递 DPK300548804377',
            trigger: 'blur',
            pattern: /^[0-9a-zA-Z\-]{1,}$/
          }
        ]
      },
      type: 1,
      // 发票凭证相关
      invoiceFileList: [],
      uploadUrl: '', // 将动态设置
      uploadHeaders: {},
      qrDialogVisible: false,
      qrCodeUrl: '',
      previewDialogVisible: false,
      previewImageUrl: '',
      pollingInterval: null,
      uploadUuid: '', // 添加uuid字段
      lastImageCount: 0, // 记录上次检查的图片数量
      qrStartTime: null // 记录二维码生成时间
    }
  },

  created() {
    // this.paramsData.orderId = this.$route.params.orderId
    // this.getCompany()
    // this.getDetail()
  },

  beforeDestroy() {
    // 清除轮询
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }
  },

  methods: {
    getDetail(orderId, type) {
      this.type = type
      this.orderId = orderId
      api_order.detail(orderId).then((response) => {
        this.orderData = response
        const orderData = response
        for (const i in orderData.orderItems) {
          if (orderData.drugType === 1) {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 100)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 1000)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        // 物流变更
        if (type === 2) {
          this.$set(this.paramsData, 'expressId', parseInt(response.expressId))
          this.$set(this.paramsData, 'deliveryId', response.deliveryId)
        }
        this.orderData = orderData
        console.log(this.orderData, 93)

        // 获取发票凭证列表
        this.getInvoiceImageList()
      })
    },
    getCompany() {
      api_order.company().then((response) => {
        this.companyData = response
      })
    },
    sureSend(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          const queryStr = this.orderId + '/' + this.paramsData.expressId + '/' + this.paramsData.deliveryId
          api_order.sendGood(queryStr).then((response) => {
            this.$message({
              message: '发货成功',
              type: 'success'
            })
            this.getDetail(this.orderId)
            this.goBack()
          })
        }
      })
    },
    goBack() {
      this.$emit('goback')
    },
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    },
    resetTemp() {
      this.$nextTick(() => {
        this.paramsData = {}
        this.invoiceFileList = []
        this.$refs['dataForm'].clearValidate()
      })
    },

    // 发票凭证相关方法
    async handleLocalUpload() {
      try {
        // 先获取上传UUID
        await this.getUploadUuid()
        // 配置上传URL和请求头
        this.configureUpload()
        // 触发文件选择
        this.$refs.invoiceUpload.$el.querySelector('input').click()
      } catch (error) {
        console.error('准备上传失败:', error)
        this.$message.error('准备上传失败，请重试')
      }
    },

    // 获取上传UUID
    async getUploadUuid() {
      try {
        if (!this.orderData.orderSn) {
          throw new Error('订单号不存在')
        }
        const response = await api_order.getUuid(this.orderData.orderSn)
        this.uploadUuid = response.uuid || response
        console.log('获取上传UUID成功:', this.uploadUuid)
      } catch (error) {
        console.error('获取上传UUID失败:', error)
        throw error
      }
    },

    // 配置上传参数
    configureUpload() {
      if (!this.uploadUuid) {
        throw new Error('上传UUID不存在')
      }
      this.uploadUrl = process.env.VUE_APP_BASE_API + `/order/shipping/img/upload/${this.uploadUuid}`
      this.uploadHeaders = {
        Authorization: getToken()
      }
      console.log('配置上传参数:', { uploadUrl: this.uploadUrl, uuid: this.uploadUuid })
    },

    async handleQrUpload() {
      try {
        // 参数验证
        if (!this.orderId) {
          this.$message.error('订单ID不能为空')
          return
        }

        const token = getToken()
        if (!token) {
          this.$message.error('用户未登录，请重新登录')
          return
        }

        // 先显示弹窗
        this.qrDialogVisible = true
        this.qrCodeUrl = '' // 清空URL以显示加载状态

        // 等待DOM更新后再生成二维码
        await this.$nextTick()

        // 检查Canvas元素是否存在
        const canvas = this.$refs.qrCanvas
        if (!canvas) {
          console.error('Canvas元素未找到')
          this.$message.error('二维码容器未准备好，请重试')
          this.qrDialogVisible = false
          return
        }

        // 清除之前的Canvas内容
        const ctx = canvas.getContext('2d')
        ctx.clearRect(0, 0, canvas.width, canvas.height)

        // 获取上传UUID
        await this.getUploadUuid()

        // 生成用于上传的URL，包含UUID参数
        this.qrCodeUrl = `${window.location.origin}/upload-mobile?orderId=${this.orderId}&orderSn=${this.orderData.orderSn}&token=${token}&uuid=${this.uploadUuid}`

        console.log('生成二维码URL:', this.qrCodeUrl)

        // 生成二维码
        await QRCode.toCanvas(canvas, this.qrCodeUrl, {
          width: 200,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })

        console.log('二维码生成成功')

        // 记录二维码生成时间和当前图片数量
        this.qrStartTime = Date.now()
        this.lastImageCount = this.invoiceFileList.length

        // 开始轮询检测上传的文件
        this.startPollingUploadedFiles()
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.$message.error(`生成二维码失败: ${error.message || '未知错误'}`)
        this.qrDialogVisible = false
      }
    },

    startPollingUploadedFiles() {
      // 清除之前的轮询
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
      }

      this.pollingInterval = setInterval(() => {
        this.checkUploadedFiles()
      }, 2000) // 改为2秒检查一次，提高同步速度
    },

    async checkUploadedFiles() {
      try {
        console.log('开始检查上传文件...')

        // 🔥 改用服务器端API检查，解决跨域问题
        // 1. 首先检查localStorage（本地环境兼容）
        const qrScanned = JSON.parse(localStorage.getItem('qrCodeScanned') || '{}')
        const uploadSync = JSON.parse(localStorage.getItem('uploadSyncFlag') || '{}')

        // 修复时间差 NaN 问题
        const timeDiff = qrScanned.timestamp ? Date.now() - qrScanned.timestamp : NaN
        console.log('localStorage时间差:', timeDiff)

        const hasQrScannedLocal = Number(qrScanned.orderId) === Number(this.orderId) &&
                                 qrScanned.timestamp &&
                                 typeof qrScanned.timestamp === 'number' &&
                                 (Date.now() - qrScanned.timestamp < 60000) // 1分钟内的扫码访问

        const hasUploadSyncLocal = Number(uploadSync.orderId) === Number(this.orderId) &&
                                  uploadSync.uploadSuccess &&
                                  uploadSync.timestamp &&
                                  typeof uploadSync.timestamp === 'number' &&
                                  (Date.now() - uploadSync.timestamp < 120000) // 2分钟内的上传成功

        // 2. 检查服务器端状态（测试环境主要依赖这个）
        let hasServerUpload = false
        let shouldCloseDialog = false

        try {
          if (this.orderData.orderSn && this.qrStartTime) {
            const currentImageList = await api_order.getInvoiceImgList(this.orderData.orderSn)
            const currentCount = currentImageList ? currentImageList.length : 0

            // 如果服务器端图片数量比二维码生成时增加了，说明有新上传
            if (currentCount > this.lastImageCount) {
              hasServerUpload = true
              console.log('检测到服务器端图片数量增加:', {
                initial: this.lastImageCount,
                current: currentCount,
                qrStartTime: this.qrStartTime
              })
            }

            // 如果二维码生成超过30秒且没有localStorage标识，可能是跨域问题，尝试关闭弹框
            const timeSinceQrStart = Date.now() - this.qrStartTime
            if (timeSinceQrStart > 30000 && !hasQrScannedLocal && this.qrDialogVisible) {
              shouldCloseDialog = true
              console.log('二维码生成超过30秒，可能存在跨域问题，尝试关闭弹框')
            }
          }
        } catch (error) {
          console.log('检查服务器端状态失败:', error)
        }

        // 🔥 如果检测到扫码访问（localStorage方式），立即关闭弹框
        if (hasQrScannedLocal && this.qrDialogVisible) {
          this.qrDialogVisible = false
          this.$message.success('检测到用户已扫码，正在准备上传页面...')
          localStorage.removeItem('qrCodeScanned')
          console.log('检测到扫码访问，已自动关闭二维码弹框')
        }

        // 🔥 如果检测到可能的跨域问题，也关闭弹框
        if (shouldCloseDialog && this.qrDialogVisible) {
          this.qrDialogVisible = false
          this.$message.info('已为您关闭二维码弹框，请继续等待图片同步...')
          console.log('因跨域问题自动关闭二维码弹框')
        }

        // 🔥 如果检测到上传成功（localStorage或服务器端），刷新图片列表
        if (hasUploadSyncLocal || hasServerUpload) {
          console.log('检测到上传成功，开始刷新图片列表', { localStorage: hasUploadSyncLocal, server: hasServerUpload })

          if (hasUploadSyncLocal) {
            localStorage.removeItem('uploadSyncFlag') // 清除标识避免重复触发
          }

          // 关闭弹框（如果还开着）
          if (this.qrDialogVisible) {
            this.qrDialogVisible = false
            this.$message.success('移动端上传成功！')
          }

          // 直接调用接口刷新图片列表
          this.getInvoiceImageList()
        }
      } catch (error) {
        console.error('检查上传文件失败:', error)
      }
    },

    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isValidType = ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)
      const isLt4M = file.size / 1024 / 1024 < 4

      if (!isImage || !isValidType) {
        this.$message.error('只支持上传 .png、.jpg、.jpeg 格式的图片文件!')
        return false
      }

      if (!isLt4M) {
        this.$message.error('上传图片大小不能超过 4MB!')
        return false
      }

      if (this.invoiceFileList.length >= 4) {
        this.$message.error('最多只能上传4张图片!')
        return false
      }

      return true
    },

    handleUploadSuccess(response, file, fileList) {
      if (response.code === 0 || response.success) {
        this.$message.success('图片上传成功')

        // 记录上传的文件信息，用于调试
        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          console.log('上传成功的文件路径:', response.data.map(item => item.path))
        }

        // 重新获取图片列表以同步最新数据
        this.getInvoiceImageList()
        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('invoiceFiles')
        })
      } else {
        this.$message.error(response.msg || response.message || '上传失败')
      }
    },

    handleUploadError(error, file, fileList) {
      console.error('上传失败:', error)
      this.$message.error('图片上传失败，请重试')
    },

    handleRemoveFile(file, fileList) {
      const index = this.invoiceFileList.findIndex((item) => item.uid === file.uid)
      if (index > -1) {
        this.invoiceFileList.splice(index, 1)
        // 触发表单验证
        this.$nextTick(() => {
          this.$refs.dataForm.validateField('invoiceFiles')
        })
      }
    },

    handleExceed(files, fileList) {
      this.$message.warning('最多只能上传4张图片')
    },

    removeImage(index) {
      this.$confirm('确定要删除这张图片吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          try {
            const file = this.invoiceFileList[index]
            // 如果有文件ID，调用删除接口
            if (file.id && this.orderData.orderSn) {
              await api_order.deleteInvoiceImg(this.orderData.orderSn, file.id)
              console.log('服务器删除文件成功:', file.id)
            }

            // 删除前端列表中的文件
            this.invoiceFileList.splice(index, 1)
            this.$message.success('删除成功')

            // 触发表单验证
            this.$nextTick(() => {
              this.$refs.dataForm.validateField('invoiceFiles')
            })
          } catch (error) {
            console.error('删除文件失败:', error)
            this.$message.error('删除文件失败，请重试')
          }
        })
        .catch(() => {
          // 取消删除
        })
    },

    previewImage(url) {
      this.previewImageUrl = url
      this.previewDialogVisible = true
    },

    // 发票文件验证器
    validateInvoiceFiles(rule, value, callback) {
      // 发货凭证改为非必填项，直接通过验证
      callback()
    },

    // 关闭二维码弹窗时的处理
    handleQrDialogClose() {
      // 延迟清除轮询，让用户有时间完成移动端上传
      // 如果弹框关闭，继续轮询3分钟以确保能同步到移动端上传的文件
      if (this.pollingInterval) {
        console.log('二维码弹框已关闭，将在3分钟后停止轮询同步')

        // 3分钟后清除轮询
        setTimeout(() => {
          if (this.pollingInterval) {
            clearInterval(this.pollingInterval)
            this.pollingInterval = null
            console.log('轮询同步已停止')
          }
        }, 180000) // 3分钟延迟 (180秒)
      }
    },

    // 获取发票凭证图片列表
    async getInvoiceImageList() {
      try {
        if (!this.orderData.orderSn) return

        const response = await api_order.getInvoiceImgList(this.orderData.orderSn)
        if (response && Array.isArray(response)) {
          if (response.length > 0) {
            this.invoiceFileList = response.map(item => ({
              id: item.id,
              name: item.fileName || item.name,
              url: item.path,
              uid: item.id || item.uid
            }))
            console.log('获取发票凭证列表成功:', this.invoiceFileList)
          } else {
            // 处理空列表情况
            this.invoiceFileList = []
            console.log('发票凭证列表为空')
          }
        } else {
          // 处理响应格式异常情况
          this.invoiceFileList = []
          console.log('发票凭证列表响应格式异常:', response)
        }
      } catch (error) {
        console.error('获取发票凭证列表失败:', error)
        this.invoiceFileList = []
      }
    }
  }
}
</script>

<style scoped>
.dtitle {
  background-color: #f5f7fa;
  font-size: 14px;
  padding: 0 20px;
  font-weight: 500;
  line-height: 40px;
  border: 1px solid #dfe4ed;
  border-bottom: none;
}
table {
  width: 100%;
  background-color: #dfe6ec;
  text-align: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #909399;
}
table tr td {
  background-color: #fff;
  padding: 10px;
}
.imgbox {
  border: 1px solid #dfe6ec;
}
.imgbox .line {
  border-bottom: 1px solid #dfe6ec;
}
.totalprice {
  padding: 0 10px;
  line-height: 40px;
  text-align: right;
  font-size: 14px;
  color: #909399;
}
.totalprice .red {
  color: #f00;
}
.totalprice .blue {
  color: blue;
}

/* 发票凭证样式 */
.invoice-upload-container {
  width: 100%;
}

.upload-buttons {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.upload-buttons .el-button {
  margin-right: 15px;
}

.upload-tips-text {
  margin-left: 15px;
  flex: 1;
}

.tip-line {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
  line-height: 1.4;
}

.tip-line:last-child {
  margin-bottom: 0;
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
}

.image-preview-item {
  position: relative;
  width: 148px;
  height: 148px;
  border: 1px dashed #c0ccda;
  border-radius: 6px;
  overflow: hidden;
  background-color: #fbfdff;
}

.image-preview-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-actions {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.image-preview-item:hover .image-actions {
  opacity: 1;
}

.image-actions i {
  color: white;
  font-size: 20px;
  margin: 0 10px;
  cursor: pointer;
}

.image-actions i:hover {
  transform: scale(1.2);
}

.qr-container {
  text-align: center;
  padding: 20px;
}

.qr-code-area {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

.qr-tips {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.qr-url {
  font-size: 12px;
  color: #999;
  word-break: break-all;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  color: #666;
}

.qr-loading i {
  font-size: 24px;
  margin-bottom: 10px;
  color: #409eff;
}
</style>
