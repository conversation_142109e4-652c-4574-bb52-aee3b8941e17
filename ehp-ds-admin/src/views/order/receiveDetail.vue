<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button v-waves type="primary" @click="goBack()">返回</el-button>
      <el-button v-waves type="primary" @click="confirm()">确认变更</el-button>
    </div>
    <div class="dtitle">收货人信息</div>
    <div class="imgbox" style="padding:20px 20px 0; font-size: 14px;margin-bottom:10px;">
        <el-form :inline="true" :model="ruleForm" ref="form" :rules="rules">
        <el-form-item label="收货人" prop="receiver">
            <el-input v-model="ruleForm.receiver" placeholder="收货人"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
            <el-input v-model="ruleForm.phone" placeholder="电话"></el-input>
        </el-form-item>
        <el-form-item label="邮编">
            <el-input v-model="ruleForm.zipcode" placeholder="邮编"></el-input>
        </el-form-item>
        <el-form-item label="地区" prop="cityId">
            <el-cascader v-model="ruleForm.cityId" :options="cityData" :props="props" clearable />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
            <el-input style="width:300px" type="textarea" :rows="3" v-model="ruleForm.address"></el-input>
        </el-form-item>
        </el-form>
    </div>
    <Detail :order-data="orderData" :receive="false" />
  </div>
</template>
<style></style>
<script scoped>
import waves from '@/directive/waves'
import Detail from './components/detail'
import api_order from '@/api/order/index'
export default {
  name: 'ReceiveDetail',
  directives: { waves },
  components: {
    Detail
  },
  data() {
    return {
      orderId: null,
      orderData: {
        orderUser: {}
      },
      labelPosition: 'right',
      cityData: null,
      orderId: '',
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      ruleForm: {},
      rules: {
        receiver: [{ required: true, message: '请输入收货人', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
        zipcode: [{ required: true, message: '请输入邮编', trigger: 'blur' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        cityId: [{ required: true, message: '请选择地址', trigger: 'blur' }]
      }
    }
  },
  created() {
    // this.getCityList()
  },
  methods: {
    getDetail(orderId) {
      this.orderId = orderId
      api_order.detail(orderId).then((response) => {
        const orderData = response
        for (const i in orderData.orderItems) {
          if (orderData.drugType === 1) {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 100)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 100)
            console.log('西药')
          } else {
            orderData.orderItems[i].salePrice = this.toFixedThree(orderData.orderItems[i].salePrice / 1000)
            orderData.orderItems[i].totalPrice = this.toFixedThree(orderData.orderItems[i].totalPrice / 1000)
            console.log('中药')
          }
        }
        this.ruleForm = { ...response.orderUser }
        this.orderData = orderData
      })
    },
    getCityList() {
      api_order.citylist().then((response) => {
        this.cityData = response
      })
    },
    goBack() {
      this.$refs['form'].resetFields()
      this.$emit('goback')
    },
    //(例子：0.1 --> 0.100)
    toFixedThree(num) {
      return (Math.round(num * 1000) / 1000).toFixed(3)
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.ruleForm.orderId = this.orderId
          api_order.update(this.ruleForm).then(response => {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.goBack()
          })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    }
  }
}
</script>
