<template>
  <div class="app-container">
    <div class="filter-container">
      <DatePicker
        :query-model="listQuery"
        style="width: 230px;margin-top:10px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.type"
        placeholder="单据类型"
        style="width: 120px;margin-top:10px;"
        type="return-goods-order-type"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.status"
        placeholder="单据状态"
        style="width: 120px;margin-top:10px;"
        type="return-goods-order-status"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        placeholder="原始订单号"
        style="width: 150px;margin-top:10px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.businessNo"
        clearable
        placeholder="退换货号"
        style="width: 150px;margin-top:10px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.skuCode"
        clearable
        placeholder="商品SKU码"
        style="width: 150px;margin-top:10px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.receiver"
        clearable
        placeholder="收货人"
        style="width: 150px;margin-top:10px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="收货手机"
        style="width: 150px;margin-top:10px;"
        @keyup.enter.native="handleFilter"
      />
      <div class="lbtn">
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >查询</el-button>
        <el-button
          v-waves
          type="primary"
          :disabled="
            checkListLen === confirmArr.length && confirmArr.length > 0
              ? false
              : true
          "
          @click="sureReceive"
        >确认收货</el-button>
      </div>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
      @selection-change="orderSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed
        prop="orderSn"
        width="140px"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button
            type="text"
            icon="el-icon-view"
            @click="goDetail(row.id, row.type, 1)"
          >详情</el-button>
          <el-button
            v-if="row.status == 3"
            v-permission="['oms:return:order:confirm']"
            icon="el-icon-circle-check"
            type="text"
            @click="confirmReceipt(row.id, row.type, 5)"
          >确认收货</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="退货单号"
        prop="businessNo"
        width="150px"
        align="center"
      />
      <el-table-column
        label="订单编号"
        prop="orderSn"
        width="150px"
        align="center"
      />
      <el-table-column
        label="退货状态"
        prop="statusDescribe"
        width="100px"
        align="center"
      />
      <el-table-column
        label="返仓物流公司"
        prop="logisticsCompany"
        width="150px"
        align="center"
      />
      <el-table-column
        label="返仓物流单号"
        prop="deliveryId"
        width="150px"
        align="center"
      />
      <el-table-column label="审核时间" prop="createdAt" align="center" />
    </el-table>
    <pagination
      v-show="total > 0"
      style="margin-top:0;"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style scoped>
.lbtn {
  padding: 10px 0px;
  text-align: left;
}
</style>
<script>
import api_exchange from '@/api/exchange/exchange'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import waves from '@/directive/waves' // Waves directive
export default {
  name: 'Exchangelist',
  directives: { waves },
  filters: {},
  components: {
    DictSelect,
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      checkListLen: 0,
      auditArr: [], //审核
      resetArr: [], //反审
      submitArr: [], //提交
      invalidArr: [], //作废
      confirmArr: [] //确认收货
    }
  },
  created() {
    // this.handleFilter()
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      api_exchange.orderlist(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    orderSelectionChange(value) {
      var auditArr = []
      var resetArr = []
      var submitArr = []
      var invalidArr = []
      var confirmArr = []
      if (value.length > 0) {
        value.forEach((currentValue, index, arr) => {
          if (currentValue.status === 1) {
            submitArr.push(currentValue.id)
            invalidArr.push(currentValue.id)
          } else if (currentValue.status === 2) {
            auditArr.push(currentValue.id)
            resetArr.push(currentValue.id)
          } else if (currentValue.status === 3) {
            resetArr.push(currentValue.id)
            confirmArr.push(currentValue.id)
          }
        })
      }
      this.auditArr = auditArr
      this.resetArr = resetArr
      this.submitArr = submitArr
      this.invalidArr = invalidArr
      this.confirmArr = confirmArr
      this.checkListLen = value.length
      console.log(
        this.auditArr,
        this.resetArr,
        this.submitArr,
        this.invalidArr,
        this.confirmArr,
        this.checkListLen
      )
    },
    handleUpdate(id) {},
    sureReceive() {
      this.confirmOrder(this.confirmArr)
    },
    subOrder(data) {
      this.$confirm('是否提交订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_exchange.orderSubmit(data).then(
          response => {
            this.$notify({
              title: '成功',
              message: '订单提交成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          },
          error => {}
        )
      })
    },
    invalidOrder(data) {
      this.$confirm('是否作废订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_exchange.orderInvalid(data).then(
          response => {
            this.$notify({
              title: '成功',
              message: '订单作废成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          },
          error => {}
        )
      })
    },
    confirmReceipt(id, type, ctype) {
      this.confirmArr.push(id)
      this.confirmOrder(this.confirmArr)
    },
    confirmOrder(data) {
      this.$confirm('是否确认收货订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_exchange.orderConfirm(data).then(
          response => {
            this.$notify({
              title: '成功',
              message: '订单收货成功',
              type: 'success',
              duration: 2000
            })
            this.getList()
          },
          error => {}
        )
      })
    },
    goDetail(id, type, ctype) {
      switch (type) {
        case 1:
          console.log(type, '1')
          this.$router.push({
            path: './retreat/' + id
          })
          break
        case 2:
          console.log(type, '2')
          this.$router.push({
            path: './change/' + id
          })
          break
        case 3:
          console.log(type, '3')
          this.$router.push({
            path: './repair/' + id
          })
          break
      // switch (ctype) {
      //   case 1:
      //     switch (type) {
      //       case 1:
      //         this.$router.push({
      //           path: './retreat/' + id
      //         })
      //         break
      //       case 2:
      //         this.$router.push({
      //           path: './change/' + id
      //         })
      //         break
      //       case 3:
      //         this.$router.push({
      //           path: './repair/' + id
      //         })
      //         break
      //     }
      //     break
      //   case 2:
      //     switch (type) {
      //       case 1:
      //         this.$router.push({
      //           path: '../editretreat/' + id
      //         })
      //         break
      //       case 2:
      //         this.$router.push({
      //           path: '../editchange/' + id
      //         })
      //         break
      //       case 3:
      //         this.$router.push({
      //           path: '../editrepair/' + id
      //         })
      //         break
      //     }
      //     break
      //   case 3:
      //     switch (type) {
      //       case 1:
      //         this.$router.push({
      //           path: '../verifyretreat/' + id
      //         })
      //         break
      //       case 2:
      //         this.$router.push({
      //           path: '../verifychange/' + id
      //         })
      //         break
      //       case 3:
      //         this.$router.push({
      //           path: '../verifyrepair/' + id
      //         })
      //         break
      //     }
      //     break
      //   case 4:
      //     this.subOrder([id])
      //     break
      //   case 5:
      //     this.confirmOrder([id])
      //     break
      //   case 6:
      //     this.invalidOrder([id])
      //     break
      }
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    }
  }
}
</script>
