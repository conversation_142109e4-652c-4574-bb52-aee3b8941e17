<template>
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.number"
        clearable
        placeholder="商品SKU码"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.commonName"
        clearable
        placeholder="通用名"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-cascader
        ref="myCascader"
        v-model="listQuery.pharmacologyClassificationId"
        :options="pharmacologyData"
        :props="props"
        :show-all-levels="false"
        placeholder="药理分类"
        clearable
        @change="handleFilter"
      />
      <!--DictSelect
        v-model="listQuery.status"
        placeholder="是否启用"
        style="width: 150px;"
        type="product_data_integrity"
        @change="handleFilter"
      /-->
      <!-- <el-select v-model="listQuery.status" clearable placeholder="是否启用" @change="handleFilter">
        <el-option
          v-for="item in qyStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select> -->
    </div>
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >查询</el-button>
      <el-button
        v-if="!stockStatus"
        v-permission="['warehouse:goods:inventory']"
        v-waves
        type="primary"
        @click="editStock"
      >修改库存</el-button>
      <el-button
        v-else
        v-permission="['warehouse:goods:inventory']"
        v-waves
        type="primary"
        @click="saveStock"
      >保存库存</el-button>
      <el-button
        v-waves
        v-permission="['pharmacy:warehouse:goods']"
        type="primary"
        style="position:relative;"
      ><template><input
        id="imgfile"
        class="imgfile"
        type="file"
        multiple="false"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        style="position:absolute;left:-9999px;top:0;"
        @change="handleFileChange($event)"
      />批量修改库存<label for="imgfile" style="position:absolute;left:0;top:0;bottom:0;right:0"></label></template></el-button>
      <el-button
        v-waves
        v-permission="['pharmacy:warehouse:goods']"
        type="primary"
        style="position:relative;"
        @click="downExcel"
      >批量修改库存模板下载</el-button>
    </div>
    <el-table
      ref="goodsTable"
      :key="tableKey"
      :data="goodsList"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="商品sku码" prop="number" align="center" />
      <el-table-column label="商品名称" prop="name" align="center" />
      <el-table-column label="通用名" prop="commonName" align="center" />
      <el-table-column label="商品基础分类" prop="pharmacologyClassificationName" align="center" />
      <el-table-column label="可用库存" prop="remainQuantity" align="center">
        <template slot="header" slot-scope="scope">
          <span>
            可用库存
            <el-tooltip :aa="scope" class="item" effect="dark" placement="top-start">
              <div slot="content">可用库存=实物库存-占用库存<br />占用库存：当前门店未确认/未发货订单锁定的库存数量</div>
              <i class="el-icon-question"> </i>
            </el-tooltip>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="实物库存" prop="quantity" align="center" width="130">
        <template slot="header" slot-scope="scope">
          <span>
            实物库存
            <el-tooltip :aa="scope" class="item" effect="dark" content="仓库实际剩余货物数" placement="top-start">
              <i class="el-icon-question"> </i>
            </el-tooltip>
          </span>
        </template>
        <template slot-scope="scope">
          <div v-if="!stockStatus">{{ scope.row.quantity }}</div>
          <div v-else><el-input v-model="stockList[scope.$index].quantity" style="width:100px;" placeholder="请输入数量" :value="scope.row.productId"></el-input></div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style>

</style>
<script>
import waves from '@/directive/waves' // Waves directive
import api_product from '@/api/product/index'
export default {
  name: 'Productlist',
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      stockStatus: false,
      pharmacologyData: null,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      qyStatus: [{ label: '启用', value: 0 }, { label: '停用', value: 1 }],
      goodsList: [],
      stockList: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20,
        pharmacologyClassificationId: ''
      }
    }
  },
  created() {
    this.getPharmacology()
    // this.getList()
  },
  activated() {
    this.getList()
    this.$nextTick(() => {
      this.$refs.myCascader.$refs.panel.checkedValue = []
    })
  },
  methods: {
    downExcel() {
      window.location.href = '/static/excel/批量修改库存模板.xlsx'
    },
    getList() {
      api_product.list(this.listQuery).then(response => {
        this.goodsList = response.list
        this.stockList = []
        for (let i = 0; i < this.goodsList.length; i++) {
          this.stockList.push({ skuId: this.goodsList[i].skuId, quantity: this.goodsList[i].quantity })
        }
        this.total = response.totalCount
      })
    },
    getPharmacology() {
      api_product.pharmacology().then(response => {
        this.pharmacologyData = response
      })
    },
    editStock() {
      this.stockStatus = true
    },
    saveStock() {
      this.stockStatus = false
      const changeArr = []
      for (let i = 0; i < this.goodsList.length; i++) {
        if (this.goodsList[i].skuId === this.stockList[i].skuId && this.goodsList[i].quantity !== this.stockList[i].quantity) {
          // if (this.stockList[i].quantity <= 0) {
          //   this.stockList[i].quantity = this.goodsList[i].quantity
          //   this.stockStatus = true
          //   this.$message({
          //     message: '修改库存数量只能是正整数！',
          //     type: 'error'
          //   })
          //   return false
          // }
          changeArr.push(this.stockList[i])
        }
      }
      if (changeArr.length > 0) {
        api_product.stock(changeArr).then(response => {
          this.$message({
            message: '修改库存成功',
            type: 'success'
          })
          this.getList()
        })
      }
    },
    handleFileChange(event) {
      if (!event.target.files[0]) {
        return
      }
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      api_product.excelstock(param).then(response => {
        console.log(response)
        this.$message({
          message: '批量修改库存成功',
          type: 'success'
        })
        this.getList()
      }, error => {
        // this.$message({
        //   message: '批量修改失败！',
        //   type: 'error'
        // })
        console.log(error)
      })
      event.target.value = null
      // this.$set(this.skuData,'images',images)
      console.log('上传')
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    }
  }
}
</script>
