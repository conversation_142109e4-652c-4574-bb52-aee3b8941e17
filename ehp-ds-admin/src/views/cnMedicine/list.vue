<template>
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.name"
        placeholder="药材名称"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.alias"
        placeholder="别名"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
    </div>
    <div class="filter-container">
      <el-button v-permission="['tcm:list']" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-show="!priceStatus" v-permission="['tcm:price']" type="primary" @click="editPrice">修改价格</el-button>
      <el-button v-show="priceStatus" v-permission="['tcm:price']" type="success" @click="savePrice">保存价格</el-button>
      <el-button v-permission="['tcm:slice:save']" type="primary" icon="el-icon-plus" @click="showDialog">添加中药</el-button>
      <el-upload
        v-show="activeName === 'pieces'"
        v-permission="['tcm:batch:price']"
        style="display: inline-block;float: none;margin-left: 10px;margin-bottom: 0;"
        :action="uploadPath"
        :headers="headers"
        :show-file-list="false"
        :file-list="recordInfo.workContracts"
        :before-upload="beforeAvatarUpload"
        :on-success="handlerSuccess"
        :on-error="handlerError"
        accept=".xls,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      >
        <el-button type="primary">批量导入价格</el-button>
      </el-upload>
      <el-button v-if="activeName === 'pieces'" type="primary" @click="handleExport">下载导入模板</el-button>
    </div>
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <!-- <el-tab-pane name="grain" label="中药配方颗粒" lazy>
        <el-table :key="tableKey1" :data="grainList" fit highlight-current-row>
          <el-table-column label="药材ID" prop="id" align="center">
            <template slot-scope="{row}">
              <el-button
                type="text"
                size="mini"
                @click="showTheDetail(row)"
              >{{ row.id }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="药材名称" prop="name" align="center">
            <template slot-scope="{row}">
              <el-button
                type="text"
                size="mini"
                @click="showTheDetail(row)"
              >{{ row.name }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="别名" prop="alias" align="center" width="150px" />
          <el-table-column label="功能主治" prop="indications" align="center" width="250px" />
          <el-table-column label="规格" prop="spec" align="center" />
          <el-table-column label="当量" prop="equivalent" align="center" />
          <el-table-column label="生产厂家" prop="productionEnterpriseName" align="center" />
          <el-table-column label="药材类型" prop="typeDescribe" align="center" />
          <el-table-column label="销售价格" prop="price" align="center" width="150px" />
          <el-table-column label="自定义价格" prop="customPrice" align="center">
            <template slot-scope="scope">
              <div v-if="!priceStatus">{{ scope.row.customPrice }}</div>
              <div v-else><el-input v-model="grainList[scope.$index].customPrice" style="width:100px;" placeholder="请输入数量" @keyup.native="format2Num($event, grainList[scope.$index].customPrice)"></el-input></div>
            </template>
          </el-table-column>
          <el-table-column label="自定义价格状态" prop="customPriceStatusDescribe" width="200px" align="center">
            <template slot-scope="{row}">
              <el-tag size="small" :type="row.status === 3 ? 'danger' : ''">
                {{ row['customPriceStatusDescribe'] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="250px">
            <template slot-scope="{row}">
              <el-button
                v-permission="['tcm:slice:alias']"
                type="text"
                @click="editOtherName(row.sliceId)"
              >添加别名</el-button>
              <el-button
                v-permission="['tcm:price']"
                type="text"
                @click="setThePrice(row)"
              >修改价格</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total1 > 0"
          :total="total1"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane> -->
      <!-- 后台增加两列 自定义价格  状态添加编辑按钮 自己的可以编辑 修改别名 页面做列表 -->
      <el-tab-pane name="pieces" label="中药饮片" lazy>
        <el-table :key="tableKey2" :data="piecesList" fit highlight-current-row>
          <el-table-column label="药材ID" prop="id" align="center">
            <template slot-scope="{row}">
              <el-button
                type="text"
                size="mini"
                @click="showTheDetail(row)"
              >{{ row.id }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="药材名称" prop="name" align="center">
            <template slot-scope="{row}">
              <el-button
                type="text"
                size="mini"
                @click="showTheDetail(row)"
              >{{ row.name }}</el-button>
            </template>
          </el-table-column>
          <el-table-column label="别名" prop="alias" align="center" width="150px" />
          <el-table-column label="功能主治" prop="indications" align="center" width="250px" />
          <el-table-column label="生产厂家" prop="productionEnterpriseName" align="center">
            <template slot-scope="{row}">
              <p v-if="row['productionEnterpriseName']">{{ row['productionEnterpriseName'] }}</p>
              <el-button v-else type="text" size="mini" @click="showRelation(row)">选择关联厂家</el-button>
            </template>
          </el-table-column>
          <el-table-column label="药材类型" prop="typeDescribe" align="center" />
          <el-table-column label="销售价格" prop="price" align="center" width="150px">
            <template slot-scope="scope">
              <div v-if="!priceStatus">{{ scope.row.price }}</div>
              <div v-else>
                <el-input v-model="piecesList[scope.$index].price" style="width:100px;" placeholder="请输入价格" @keyup.native="format2Num($event, piecesList[scope.$index].price)"></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center" width="250px">
            <template slot-scope="{row}">
              <el-button
                v-permission="['tcm:slice:alias']"
                type="text"
                @click="editOtherName(row.id)"
              >添加别名</el-button>
              <el-button
                v-permission="['tcm:price']"
                type="text"
                @click="setThePrice(row)"
              >修改价格</el-button>
              <el-button
                v-permission="['tcm:slice:pe']"
                type="text"
                style="margin-top: 10px;"
                @click="showRelation(row)"
              >选择关联厂家</el-button>
              <el-button
                v-show="row.dataType === 2"
                v-permission="['tcm:slice:update']"
                type="text"
                style="margin-top: 10px;"
                @click="editTheSlice(row)"
              >编辑</el-button>
              <el-button
                v-show="row.price"
                v-permission="['tcm:slice:delete']"
                type="text"
                style="margin-top: 10px;"
                @click="delTheSlice(row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total2 > 0"
          :total="total2"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>
    <el-dialog :title="productData.id? '编辑' : '添加'" :visible.sync="dialogFormVisible" width="1000px">
      <el-form
        ref="dataForm"
        :model="productData"
        :rules="rules"
        :inline="true"
        label-width="120px"
        class="demo-form-inline"
      >
        <el-input v-model="productData.id" type="hidden" />
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药材名" prop="name">
              <el-input v-model="productData.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              <el-input v-model="productData.price" clearable @keyup.native="formatPrice">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="别名" prop="brandName">
              <el-input v-model="alias" clearable>
                <el-button slot="append" @click="addAlias">添加</el-button>
              </el-input>
            </el-form-item>
            <el-tag
              v-for="(tag, i) in productData.alias"
              :key="tag"
              closable
              size="mini"
              @close="removeTag(i)"
            >{{ tag }}
            </el-tag>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产企业" prop="productionEnterpriseId">
              <el-select v-model="productData.productionEnterpriseId" placeholder="请选择">
                <el-option
                  v-for="item in producer"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :gutter="20" style="margin-top: 4px">找不到相关药企请联系管理员进行添加</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="炮制方法" prop="processing">
              <el-input
                v-model="productData.processing"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="贮藏" prop="storage">
              <el-input
                v-model="productData['storage']"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性状" prop="phenotypicTrait">
              <el-input v-model="productData['phenotypicTrait']" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="化学成分" prop="ingredients">
              <el-input
                v-model="productData['ingredients']"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="功能主治" prop="indications">
              <el-input
                v-model="productData.indications"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用法用量" prop="usageDosage">
              <el-input
                v-model="productData.usageDosage"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="来源" prop="source">
              <el-input
                v-model="productData.source"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原形态" prop="protomorphic">
              <el-input
                v-model="productData['protomorphic']"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="毒性" prop="toxicity">
              <el-input
                v-model="productData['toxicity']"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药理作用" prop="pharmaco">
              <el-input
                v-model="productData['pharmaco']"
                type="textarea"
                width="50%"
                :autosize="autosize"
                placeholder=""
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性味" prop="sexualFlavour">
              <DictSelect v-model="productData['sexualFlavour']" placeholder="请选择" :multiple="true" type="tcm-slice-sf" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归经" prop="channelTropism">
              <DictSelect v-model="productData['channelTropism']" :multiple="true" placeholder="请选择" type="tcm-slice-channeltropism" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药性" prop="drugProperties">
              <DictSelect v-model="productData['drugProperties']" placeholder="请选择" type="tcm-slice-drugproperties" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出处" prop="provenance">
              <el-input v-model="productData['provenance']" clearable :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="注意事项" prop="mattersNeedingAttention" class="form_100">
              <el-input
                v-model="productData['mattersNeedingAttention']"
                type="textarea"
                :autosize="autosize"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button type="primary" @click="nextCreate('dataForm')">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="添加别名" :visible.sync="dialogOtherVisible" width="400px">
      <el-form
        ref="dataForm"
        :model="productData"
        :rules="rules"
        :inline="true"
        label-width="120px"
        class="demo-form-inline"
        @submit.native.prevent
      >
        <el-tag
          v-for="(tag,index) in dynamicTags"
          :key="index"
          closable
          :disable-transitions="false"
          @close="handleClose(tag,index)"
        >{{ tag.name }}
        </el-tag>
        <el-input
          v-if="inputVisible"
          ref="saveTagInput"
          v-model="inputValue"
          class="input-new-tag"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
        >
        </el-input>
        <el-button v-else class="button-new-tag" size="small" @click="showInput">+ 添加别名</el-button>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogOtherVisible = false">返回</el-button>
        <el-button type="primary" @click="nextSetData">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="选择关联厂家" :visible.sync="dialogGMPVisible" width="400px">
      <el-form
        ref="peForm"
        :model="peData"
        :rules="rules"
        :inline="true"
        label-width="120px"
      >
        <el-form-item label="生产企业" prop="peId">
          <el-select v-model="peData.peId" placeholder="请选择">
            <el-option
              v-for="item in producer"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogGMPVisible = false">返回</el-button>
        <el-button type="primary" @click="nextRelation">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="详情" :visible.sync="dialogDetailVisible" width="1000px">
      <el-form
        ref="dataForm"
        :model="productData"
        :inline="true"
        label-width="120px"
        class="demo-form-inline"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药材名" prop="name">
              {{ productData.name }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价格" prop="price">
              {{ productData.price }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="别名" prop="brandName">
              {{ productData.alias? productData.alias.join('、') : '' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产企业" prop="productionEnterpriseId">
              <el-select v-model="productData.productionEnterpriseId" disabled placeholder="请选择">
                <el-option
                  v-for="item in producer"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="炮制方法" prop="processing">
              {{ productData.processing }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="贮藏" prop="storage">
              {{ productData['storage'] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性状" prop="phenotypicTrait">
              {{ productData['phenotypicTrait'] }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="化学成分" prop="ingredients">
              {{ productData['ingredients'] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="功能主治" prop="indications">
              {{ productData.indications }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用法用量" prop="usageDosage">
              {{ productData.usageDosage }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="来源" prop="source">
              {{ productData.source }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原形态" prop="protomorphic">
              {{ productData['protomorphic'] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="毒性" prop="toxicity">
              {{ productData['toxicity'] }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药理作用" prop="pharmaco">
              {{ productData['pharmaco'] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="性味" prop="sexualFlavour">
              <DictSelect v-model="productData['sexualFlavour']" :disabled="true" :multiple="true" placeholder="请选择" type="tcm-slice-sf" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归经" prop="channelTropism">
              <DictSelect v-model="productData['channelTropism']" :disabled="true" :multiple="true" placeholder="请选择" type="tcm-slice-channeltropism" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="药性" prop="drugProperties">
              <DictSelect v-model="productData['drugProperties']" :disabled="true" placeholder="请选择" type="tcm-slice-drugproperties" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出处" prop="provenance">
              {{ productData['provenance'] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="注意事项" prop="mattersNeedingAttention" class="form_100">
              {{ productData['mattersNeedingAttention'] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogDetailVisible = false">返回</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken, getTokenName } from '@/utils/auth'
import api_count from '@/api/count/count'
import DictSelect from '@/components/DictSelect'
import { particleList, sliceData, gmpSelect, setCnMedicine, setPrice, setSlicePrice, aliasData, setAlias, setPe, sliceInfo, particleInfo, delSlice, excelURL } from '../../api/cnMedicine/list'
export default {
  name: '',
  filters: {},
  components: {
    DictSelect
  },
  data() {
    return {
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      grainList: [],
      total1: 0,
      piecesList: [],
      total2: 0,
      tableKey1: 1,
      tableKey2: 2,
      dialogFormVisible: false,
      dialogOtherVisible: false,
      dialogGMPVisible: false,
      dialogDetailVisible: false,
      alias: '',
      productData: {},
      activeName: 'pieces',
      priceStatus: false,
      uploadPath: excelURL(),
      headers: {
        [getTokenName()]: getToken()
      },
      recordInfo: {
        hospitalInfo: {},
        userBaseInfo: {},
        userInfo: {},
        workInfos: [],
        workContracts: []
      },
      producer: [],
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      sliceId: '',
      peData: {
        peId: ''
      },
      autosize: { minRows: 2, maxRows: 20 },
      rules: {
        name: [{ required: true, message: '请选择品类', trigger: 'blur' }],
        price: [{ required: true, message: '请输入价格', trigger: 'blur' }],
        peId: [{ required: true, message: '请输入生产厂家', trigger: 'change' }]
      }
    }
  },
  created() {
    this.getList()
    this.getGMPSelect()
  },
  methods: {
    // 获取数据
    getList() {
      if (this.activeName === 'grain') {
        particleList(this.listQuery).then(response => {
          this.grainList = response.list
          this.total1 = response.totalCount
          this.priceList = []
          for (let i = 0; i < this.grainList.length; i++) {
            this.priceList.push({ id: this.grainList[i].id, price: this.grainList[i].price })
          }
        })
      } else if (this.activeName === 'pieces') {
        sliceData(this.listQuery).then(response => {
          this.piecesList = response.list
          this.total2 = response.totalCount
          this.priceList = []
          for (let i = 0; i < this.piecesList.length; i++) {
            this.priceList.push({ id: this.piecesList[i].id, price: this.piecesList[i].price })
          }
        })
      }
    },
    getGMPSelect() {
      gmpSelect(this.listQuery).then(response => {
        this.producer = response
      })
    },
    showDialog() {
      this.dialogFormVisible = true
      this.productData = {}
      this.getGMPSelect()
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    setThePrice(row) {
      this.$prompt('价格', '修改价格', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.price,
        inputPattern: /^(\d+)(.\d{0,2})?$/,
        inputErrorMessage: '只能输入数字(保留两位小数)',
        inputPlaceholder: '请输入价格'
      }).then(({ value }) => {
        if (value) {
          if (value <= 0) {
            this.$message.error({ message: '只能是正数！' })
          } else {
            const idName = this.activeName === 'grain' ? 'particleId' : 'sliceIdId'
            const data = [
              {
                [idName]: row.id,
                price: value
              }
            ]
            this.changePrice(data)
          }
        } else {
          this.$message({
            message: '不能为空',
            center: true
          })
        }
      }).catch(() => {
        console.log('取消输入了')
      })
    },
    changePrice(data) {
      if (this.activeName === 'grain') {
        setPrice(data).then(res => {
          this.getList()
          this.$message.success('操作成功')
        })
      } else {
        setSlicePrice(data).then(res => {
          this.getList()
          this.$message.success('操作成功')
        })
      }
    },
    handleClick(tab, event) {
      this.activeName = tab.name
      this.listQuery = {
        pageNo: 1,
        pageSize: 10
      }
      this.getList()
    },
    format2Num(ev, target) {
      // 通过正则过滤小数点后两位
      ev.target.value = ev.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null
      target = ev.target.value
    },
    addAlias() {
      if (this.alias) {
        if (!(this.productData.alias instanceof Array)) {
          this.productData.alias = []
        }
        this.productData.alias.push(this.alias)
        this.alias = ''
        this.$forceUpdate()
      }
    },
    editPrice() {
      this.priceStatus = true
    },
    savePrice() {
      const { grainList, piecesList, priceList, activeName } = this
      const goodsList = activeName === 'grain' ? grainList : piecesList
      const price = activeName === 'grain' ? 'customPrice' : 'price'
      this.priceStatus = false
      const changeArr = []
      for (let i = 0; i < goodsList.length; i++) {
        if (goodsList[i].id === priceList[i].id && goodsList[i][price] !== priceList[i][price]) {
          priceList[i][price] = goodsList[i][price]
          if (goodsList[i][price] <= 0) {
            this.priceStatus = true
            this.$message({
              message: '只能是正数！',
              type: 'error'
            })
            return false
          }
          const idName = activeName === 'grain' ? 'particleId' : 'sliceIdId'
          changeArr.push({
            price: goodsList[i][price],
            [idName]: goodsList[i].id
          })
        }
      }
      console.log(changeArr)
      if (changeArr.length > 0) {
        this.changePrice(changeArr)
      }
    },
    removeTag(index) {
      this.productData.alias.splice(index, 1)
      this.$forceUpdate()
    },
    editOtherName(drugId) {
      this.getAliasData(drugId)
      this.sliceId = drugId
      this.dialogOtherVisible = true
    },
    getAliasData(id) {
      aliasData(id).then(res => {
        this.dynamicTags = res
      })
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          setCnMedicine(this.productData).then(res => {
            this.getList()
            this.dialogFormVisible = false
            this.$message.success('操作成功')
            this.productData = {}
          })
        }
      })
    },
    handleClose(tag, tagIdx) {
      if (tag.dataType === 1) {
        this.$message.warning('系统别名不可删除！')
      } else {
        this.dynamicTags.splice(tagIdx, 1)
      }
    },
    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },
    handleInputConfirm() {
      const { inputValue } = this
      const exist = this.dynamicTags.some(item => item.name === inputValue)
      if (inputValue && !exist) {
        this.dynamicTags.push({
          name: inputValue
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    },
    nextSetData() {
      const data = this.dynamicTags.filter(item => item.dataType !== 1).map(item => item.name)
      setAlias(this.sliceId, data).then(response => {
        this.dialogOtherVisible = false
        this.$message.success('操作成功')
        this.dynamicTags = []
        this.getList()
      })
    },
    showRelation(row) {
      this.dialogGMPVisible = true
      this.$nextTick(function() {
        this.$refs['peForm'].resetFields()
        this.peData.peId = row.productionEnterpriseId ? row.productionEnterpriseId : ''
        this.sliceId = row.id
      }, 100)
    },
    nextRelation() {
      this.$refs['peForm'].validate(valid => {
        if (valid) {
          const { sliceId, peData } = this
          setPe(sliceId, peData.peId).then(res => {
            this.$message.success('操作成功')
            this.dialogGMPVisible = false
            this.getList()
          })
        }
      })
    },
    editTheSlice(row) {
      sliceInfo(row.id).then(res => {
        this.productData = res
        this.productData.aliasStr = res.alias ? res.alias.join('、') : ''
        this.dialogFormVisible = true
      })
    },
    delTheSlice(row) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delSlice(row.id).then(res => {
          this.$message.success('删除成功')
          this.getList()
        })
      })
    },
    showTheDetail(row) {
      if (this.activeName === 'grain') {
        particleInfo(row.id).then(res => {
          this.productData = { ...res.sliceDetails, ...res }
          this.dialogDetailVisible = true
        })
      } else {
        sliceInfo(row.id).then(res => {
          this.productData = res
          this.dialogDetailVisible = true
        })
      }
    },
    dataOut() {
      const queryExport = this.listQuery
      delete queryExport.pageNo
      delete queryExport.pageSize
      queryExport[getTokenName()] = getToken()
      if (this.activeName === 'grain') {
        const url = api_count.orderExport()
        window.location.href = url + this.urlEncode(queryExport)
      } else if (this.activeName === 'pieces') {
        const url = api_count.productExport()
        window.location.href = url + this.urlEncode(queryExport)
      }
    },
    urlEncode(param, key, encode) {
      if (param === null) return ''
      var paramStr = ''
      var t = typeof param
      if (t === 'string' || t === 'number' || t === 'boolean') {
        paramStr +=
          '&' +
          key +
          '=' +
          (encode === null || encode ? encodeURIComponent(param) : param)
      } else {
        for (var i in param) {
          var k =
            key === undefined
              ? i
              : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
          paramStr += this.urlEncode(param[i], k, encode)
        }
      }
      return paramStr
    },
    beforeAvatarUpload(file) {
      const fileType = file.name.substring(file.name.lastIndexOf('.') + 1)
      const extension = fileType === 'xls'
      const extension2 = fileType === 'xlsx'
      if (!extension && !extension2) {
        this.$message({
          message: '上传文件只能是 xls、xlsx格式!',
          type: 'warning'
        })
      }
      return extension || extension2
    },
    handlerSuccess(res) {
      // this.$message.success('成功')
      if (res.code === 0) {
        this.$message.success('成功')
        this.$nextTick(function() {
          this.getList()
        }, 100)
      } else {
        this.$message.error(res.msg)
      }

    },
    handlerError(err) {
      console.log(err)
      this.$message.error(err.msg)
    },
    handleExport() {
      window.location.href = '/static/excel/批量导入价格模板.xls'
    },
    formatPrice(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,5})/g)[0] || null
      this.productData.price = e.target.value
    }
  }
}
</script>

<style scoped>
  .mrt8{
    margin-top: 4px;
    margin-right: 8px;
    margin-bottom: 4px;
  }
  .el-tag {
    margin-left: 0;
    margin-right: 10px;
    margin-bottom: 10px;
  }
  /deep/.el-tag--mini{
    margin-top: 1px;
    height: auto;
    padding: 3px 5px;
  }
  .button-new-tag {
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
  }
  .demo-form-inline /deep/.el-form-item__content{
    max-width: 310px;
  }
  .form_100 /deep/.el-form-item__content{
    width: 100%;
    max-width: 800px;
  }
  /deep/.el-form-item--mini.el-form-item{
    width: 100%;
  }
  /deep/.el-input{
    width: 100%;
  }
</style>
