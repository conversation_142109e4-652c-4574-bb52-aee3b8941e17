/* eslint-disable vue/no-v-html */
<template>
  <div class="login">
    <el-form
      ref="loginForm"
      :model="loginForm"
      :rules="loginRules"
      label-position="left"
      label-width="0px"
      class="login-form"
    >
      <h3 class="title">{{ title }}</h3>
      <el-form-item prop="username">
        <el-input v-model="loginForm.username" type="text" placeholder="账号" clearable>
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input v-model="loginForm.password" type="password" placeholder="密码" show-password clearable>
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="code">
        <el-input
          v-model.trim="loginForm.code"
          placeholder="验证码"
          style="width: 63%"
          clearable
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" />
        </div>
      </el-form-item>
      <el-form-item prop="smsCode">
        <el-input v-model="loginForm.smsCode" placeholder="请输入短信验证码" style="width: 63%" clearable @keyup.enter.native="handleLogin" @input="smsCodeInput">
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <el-button size="medium" :disabled="codetime>0" type="primary" @click.native.prevent="getMsCode">
            <span>{{ codetext }}</span>
          </el-button>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住我</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button :loading="loading" :disabled="!loginForm.username || !loginForm.password || !loginForm.code || !isGetCode" size="medium" type="primary" style="width:100%;" @click.native.prevent="handleLogin">
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div v-if="showFooter" id="el-login-footer">
      <span v-html="footerTxt" />
      <span>⋅</span>
      <a href="http://www.baidu.com/" target="_blank">{{ caseNumber }}</a>
    </div>
  </div>
</template>

<script>
import waves from '@/directive/waves' // Waves directive
import settings from '@/settings'
import { encrypt } from '@/utils/rsaEncrypt'
import { getCodeImg, getVerification } from '@/api/system/login'
export default {
  name: 'Login',
  directives: { waves },
  data() {
    return {
      title: settings.title,
      showFooter: settings.showFooter,
      footerTxt: settings.footerTxt,
      caseNumber: settings.caseNumber,
      loginForm: {
        username: '',
        password: '',
        code: '',
        smsCode: ''
      },
      codeUrl: '',
      loginRules: {
        username: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '用户名不能为空'
          }
        ],
        password: [
          {
            required: true,
            trigger: ['blur', 'change'],
            message: '密码不能为空'
          }
        ],
        code: [{ required: true, trigger: 'change', message: '验证码不能为空' }],
        smsCode: [{ required: true, trigger: 'change', message: '短信验证码不能为空' }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      codetime: 0, //验证码倒计时
      codetext: '获取验证码',
      isGetCode: false,
      routeMap: {
        1: '/channel/persion',
        2: '/channel/team',
        3: '/channel/salesman'
      }
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
  },
  methods: {
    getCode() {
      getCodeImg().then(res => {
        this.codeUrl = res.img
        this.loginForm.uuid = res.uuid
      })
    },
    handleLogin() {
      const user = {
        username: this.loginForm.username,
        password: this.loginForm.password,
        code: this.loginForm.code,
        smsCode: this.loginForm.smsCode,
        uuid: this.loginForm.uuid
      }
      user.password = encrypt(user.password)
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.$store.dispatch('system/user/login', user).then((res) => {
            // 因为演示经常随便切换，权限，所以默认都跳转首页
            this.$router.push({ path: '/' })
            // this.$router.push({ path: this.redirect || '/' })
          })
        } else {
          return false
        }
      })
    },
    getMsCode() {
      var fieldList = ['username', 'password', 'code']
      Promise.all(fieldList.map(item => {
        return new Promise((resolve, reject) => {
          this.$refs.loginForm.validateField(item, (valid) => {
            console.log('getMsCode', valid)
            resolve(valid)
          })
        })
      })).then(result => {
        console.log(result, 'result')
        const valid = result.every(str => {
          return str === ''
        })
        if (valid) {
          const params = {
            username: this.loginForm.username,
            password: this.loginForm.password,
            code: this.loginForm.code,
            uuid: this.loginForm.uuid
          }
          if (params.password !== this.cookiePass) {
            params.password = encrypt(params.password)
          }
          getVerification(params).then(res => {
            console.log('获取验证码成功')
            this.$message.success(res.sendResult)
            this.codetime = 59
            this.codetext = this.codetime + 's'
            const t = setInterval(() => {
              this.codetime--
              this.codetext = this.codetime + 's'
              if (this.codetime <= 0) {
                this.codetime = 0
                this.codetext = '重新获取'
                clearInterval(t)
              }
            }, 1000)
          }).catch(error => {
            this.getCode()
            // this.$message.error(error)
          })
        }
      })
    },
    smsCodeInput(value) {
      if (value) {
        this.isGetCode = true
      } else {
        this.isGetCode = false
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url(/static/images/bg-1.jpg);
  background-size: cover;
}

.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;

  .el-input {
    height: 38px;
    /deep/ input {
      height: 38px;
      line-height: 38px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  display: inline-block;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
</style>
