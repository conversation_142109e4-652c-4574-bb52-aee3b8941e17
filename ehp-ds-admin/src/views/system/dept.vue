<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        v-permission="['sys:dept:save']"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >{{ $t('table.add') }}</el-button>
    </div>
    <el-table
      row-key="id"
      :data="list"
      border
      fit
      highlight-current-row
      default-expand-all
      style="width: 100%;"
    >
      <el-table-column :label="$t('sysDept.name')" prop="name" align="left" />
      <el-table-column :label="$t('sysDept.orderNum')" prop="orderNum" width="70px" align="center" />>
      <el-table-column
        :label="$t('sysDept.createdAt')"
        prop="createdAt"
        width="155px"
        align="center"
      />
      <el-table-column
        :label="$t('table.actions')"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:dept:update']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >{{ $t('table.edit') }}</el-button>
          <el-button
            v-waves
            v-permission="['sys:dept:delete']"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >{{ $t('table.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="500px">
      <el-form
        ref="dataForm"
        :model="dept"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item :label="$t('sysDept.name')" prop="name">
          <el-input v-model="dept.name" :placeholder="$t('sysDept.name')" />
        </el-form-item>
        <el-form-item :label="$t('sysDept.parentName')" prop="parentId">
          <el-cascader
            v-model="dept.parentId"
            :options="deptList"
            :props="deptProps"
            clearable
            :show-all-levels="false"
            style="width:310px"
          />
        </el-form-item>
        <el-form-item :label="$t('sysDept.orderNum')" prop="orderNum">
          <el-input-number v-model="dept.orderNum" :min="1" label="排序号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getDeptList,
  get,
  create,
  update,
  del
} from '@/api/system/dept'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'

export default {
  name: 'DeptTable',
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        orderByField: 'orderNum',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      roleList: {},
      deptList: [],
      dept: {},
      rules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        parentId: [
          { required: true, message: '请选择上级部门', trigger: 'blur' }
        ]
      },
      deptProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  created() {
    this.getList()
    this.getDeptList()
  },
  methods: {
    getDeptList() {
      getDeptList().then(response => {
        this.deptList = response
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    handleFilter() {
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.dept = {
          orderNum: 1
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          create(this.dept).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getDeptList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.dept = response
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          update(this.dept).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getDeptList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getDeptList()
        })
      })
    }
  }
}
</script>
