<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        clearable
        :placeholder="$t('sysLog.username')"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker :query-model="listQuery" style="width: 230px" @change="handleFilter" />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >{{ $t('table.search') }}</el-button>
    </div>

    <el-table :key="tableKey" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column :label="$t('sysLog.username')" prop="username" width="70px" align="center" />
      <el-table-column :label="$t('sysLog.operation')" prop="operation" align="center" />
      <el-table-column :label="$t('sysLog.method')" prop="method" align="center" />
      <el-table-column :label="$t('sysLog.params')" prop="params" align="center" />
      <el-table-column :label="$t('sysLog.time')" prop="time" width="110px" align="center" />
      <el-table-column :label="$t('sysLog.ip')" prop="ip" width="80px" align="center" />
      <el-table-column
        :label="$t('sysUser.createdAt')"
        prop="createdAt"
        width="155px"
        align="center"
      />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getList } from '@/api/system/log'
import waves from '@/directive/waves' // Waves directive
import DatePicker from '@/components/DatePicker'
export default {
  name: 'LogTable',
  directives: { waves },
  filters: {},
  components: {
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      textMap: {
        update: '更新',
        create: '新增'
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    }
  }
}
</script>
