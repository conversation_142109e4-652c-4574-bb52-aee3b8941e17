<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        v-permission="['sys:menu:save']"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >{{ $t('table.add') }}</el-button>
    </div>
    <el-table
      row-key="id"
      :data="list"
      border
      fit
      highlight-current-row
      default-expand-all
      style="width: 100%;"
    >
      <el-table-column :label="$t('sysMenu.name')" prop="name" align="left">
        <template slot-scope="{row}">
          <svg-icon v-if="row.icon != null" :icon-class="row.icon" />
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('sysMenu.type')" prop="typeDescribe" width="60px" align="center" />
      <el-table-column :label="$t('sysMenu.orderNum')" prop="orderNum" width="70px" align="center" />>
      <el-table-column :label="$t('sysMenu.url')" prop="url" width="150px" align="center" />>
      <el-table-column :label="$t('sysMenu.perms')" prop="perms" width="200px" align="center" />
      <el-table-column
        :label="$t('sysMenu.hidden')"
        prop="hiddenDescribe"
        width="80px"
        align="center"
      />
      <el-table-column
        :label="$t('sysMenu.createdAt')"
        prop="createdAt"
        width="155px"
        align="center"
      />
      <el-table-column
        :label="$t('table.actions')"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:menu:update']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >{{ $t('table.edit') }}</el-button>
          <el-button
            v-waves
            v-permission="['sys:menu:delete']"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >{{ $t('table.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      top="2vh"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :model="menu"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item :label="$t('sysMenu.type')" prop="type">
          <DictRadio v-model="menu.type" type="menu_type" />
        </el-form-item>
        <el-form-item :label="$t('sysMenu.name')" prop="name">
          <el-input v-model="menu.name" :placeholder="$t('sysMenu.name')" />
        </el-form-item>
        <el-form-item :label="$t('sysMenu.parentId')" prop="parentId">
          <el-cascader
            v-model="menu.parentId"
            :options="menuList"
            :props="menuProps"
            clearable
            style="width:310px"
          />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" :label="$t('sysMenu.url')" prop="url">
          <el-input v-model="menu.url" :placeholder="$t('sysMenu.url')" />
        </el-form-item>
        <el-form-item
          v-if="menu.type == 1 || menu.type == 2"
          :label="$t('sysMenu.perms')"
          prop="perms"
        >
          <el-input v-model="menu.perms" :placeholder="$t('sysMenu.perms')" />
        </el-form-item>
        <el-form-item v-if="menu.type == 1" :label="$t('sysMenu.hidden')" prop="hidden">
          <DictRadio v-model="menu.hidden" type="menu_hidden" />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" :label="$t('sysMenu.orderNum')" prop="perms">
          <el-input-number v-model="menu.orderNum" :min="1" :label="$t('sysMenu.orderNum')" />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" :label="$t('sysMenu.icon')">
          <el-input v-model="menu.icon" :placeholder="$t('sysMenu.icon')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, get, getSelect, create, update, del } from '@/api/system/menu'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'
import DictRadio from '@/components/DictRadio'
export default {
  name: 'MenuTable',
  directives: { waves },
  components: { DictRadio },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      listQuery: {
        orderByField: 'orderNum',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      menu: {
        orderNum: 1
      },
      rules: {
        type: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        parentId: [
          { required: true, message: '请选择上级菜单', trigger: 'blur' }
        ]
      },
      menuList: [],
      menuProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  created() {
    this.getList()
    this.getSelect()
  },
  methods: {
    getSelect() {
      getSelect().then(response => {
        this.menuList = response
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    handleFilter() {
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.menu = {
          orderNum: 1,
          hidden: 0
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid && this.validator()) {
          create(this.menu).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getMenuList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.menu = response
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid && this.validator()) {
          update(this.menu).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getSelect()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    validator() {
      if (this.menu.type === 1) {
        if (!this.menu.url) {
          this.$notify({
            title: '参数错误',
            message: '菜单URL不能为空',
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该菜单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getSelect()
        })
      })
    }
  }
}
</script>
