<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        clearable
        :placeholder="$t('sysUser.username')"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-permission="['sys:user:list']"
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >{{ $t('table.search') }}</el-button>
      <el-button
        v-permission="['sys:user:save']"
        v-waves
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >{{ $t('table.add') }}</el-button>
    </div>

    <el-table :key="tableKey" :data="list" border fit highlight-current-row style="width: 100%;">
      <el-table-column :label="$t('sysUser.userId')" prop="id" width="70px" align="center" />
      <el-table-column :label="$t('sysUser.username')" prop="username" align="center" />
      <el-table-column :label="$t('sysUser.name')" prop="name" align="center" />
      <el-table-column :label="$t('sysUser.deptName')" prop="deptName" align="center" />
      <el-table-column :label="$t('sysUser.email')" prop="email" width="130px" align="center" />
      <el-table-column :label="$t('sysUser.mobile')" prop="mobile" width="110px" align="center" />
      <el-table-column
        :label="$t('sysUser.status')"
        prop="statusDescribe"
        width="60px"
        align="center"
      />>
      <el-table-column
        :label="$t('sysUser.createdAt')"
        prop="createdAt"
        width="155px"
        align="center"
      />
      <el-table-column
        :label="$t('table.actions')"
        align="center"
        fixed="right"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:user:update']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >{{ $t('table.edit') }}</el-button>
          <el-button
            v-waves
            v-permission="['sys:user:delete']"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >{{ $t('table.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      top="2vh"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :model="user"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item :label="$t('sysUser.username')" prop="username">
          <el-input v-model="user.username" :placeholder="$t('sysUser.username')" />
        </el-form-item>
        <el-form-item :label="$t('sysUser.name')" prop="name">
          <el-input v-model="user.name" :placeholder="$t('sysUser.name')" />
        </el-form-item>
        <el-form-item :label="$t('sysUser.deptName')" prop="deptId">
          <el-cascader
            v-model="user.deptId"
            :options="deptList"
            :props="defaultProps"
            clearable
            :show-all-levels="false"
            style="width:310px"
          />
        </el-form-item>
        <el-form-item :label="$t('sysUser.password')" prop="password">
          <el-input v-model="user.password" :placeholder="$t('sysUser.password')" type="password" />
        </el-form-item>
        <br>
        <el-form-item :label="$t('sysUser.email')" prop="email">
          <el-input v-model="user.email" :placeholder="$t('sysUser.email')" />
        </el-form-item>
        <el-form-item :label="$t('sysUser.mobile')" prop="mobile">
          <el-input v-model="user.mobile" :placeholder="$t('sysUser.mobile')" />
        </el-form-item>
        <el-form-item :label="$t('sysUser.role')" prop="roleIdList">
          <el-select v-model="user.roleIdList" multiple placeholder="请选择" style="width:100%">
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.roleName"
              :value="role.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('sysUser.status')" prop="status">
          <DictRadio v-model="user.status" type="system_status" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, get, create, update, del } from '@/api/system/user'
import { getRoleList } from '@/api/system/role'
import { getDeptList } from '@/api/system/dept'
import waves from '@/directive/waves' // Waves directive
import DictRadio from '@/components/DictRadio'
import {} from '@/utils'

export default {
  name: 'UserTable',
  directives: { waves },
  components: { DictRadio },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      roleList: [],
      deptList: [],
      user: {
        status: 1,
        deptId: null,
        roleIdList: [],
        password: ''
      },
      rules: {
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        password: [
          {
            validator: (rule, value, callback) => {
              var strlc = /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9]))(?=^[^\u4e00-\u9fa5]{0,}$).{8,20}$/
              if (this.user.id == null && value == null) {
                callback('请输入密码')
              } else if (value != null && value.match(strlc) == null) {
                callback(
                  new Error(
                    '密码过于简单有被盗风险，请保证密码大于8位，并且由大小写字母、数字，特殊符号组成'
                  )
                )
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
        deptId: [{ required: true, message: '请选择部门', trigger: 'blur' }],
        roleIdList: [{ required: true, message: '请选择角色', trigger: 'blur' }]
      },
      defaultProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    }
  },
  created() {
    this.getList()
    this.getDeptList()
    this.getRoleList()
  },
  methods: {
    getRoleList() {
      getRoleList().then(response => {
        this.roleList = response
      })
    },
    getDeptList() {
      getDeptList().then(response => {
        this.deptList = response
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.user = {
          status: 1,
          roleIdList: []
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          create(this.user).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.user = response
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          update(this.user).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>
