<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-permission="['sys:dict:save']"
        v-waves
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-edit"
        @click="handleCreate"
      >{{ $t('table.add') }}</el-button>
    </div>

    <el-table
      row-key="id"
      :data="list"
      border
      fit
      highlight-current-row
      default-expand-all
      style="width: 100%;"
    >
      <el-table-column :label="$t('sysDict.name')" prop="name" width="220px" align="left" />
      <el-table-column :label="$t('sysDict.type')" prop="type" width="220px" align="center" />
      <el-table-column :label="$t('sysDict.code')" prop="code" width="70px" align="center" />
      <el-table-column :label="$t('sysDict.value')" prop="value" align="center" />
      <el-table-column :label="$t('sysDict.orderNum')" prop="orderNum" width="50px" align="center" />
      <el-table-column :label="$t('sysDict.remark')" prop="remark" align="center" />
      <el-table-column
        :label="$t('sysDict.createdAt')"
        prop="createdAt"
        width="155px"
        align="center"
      />
      <el-table-column
        :label="$t('table.actions')"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:dict:update']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >{{ $t('table.edit') }}</el-button>
          <el-button
            v-if="row.parentId === 0"
            v-waves
            v-permission="['sys:dict:update']"
            type="primary"
            size="mini"
            @click="handleLowerLevel(row)"
          >{{ $t('table.add') }}</el-button>
          <el-button
            v-if="row.children == null"
            v-waves
            v-permission="['sys:dict:delete']"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >{{ $t('table.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="500px">
      <el-form
        ref="dataForm"
        :model="dict"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item v-if="dict.parentId === 0" :label="$t('sysDict.name')" prop="name">
          <el-input v-model="dict.name" :placeholder="$t('sysDict.name')" />
        </el-form-item>
        <el-form-item v-if="dict.parentId === 0" :label="$t('sysDict.type')" prop="type">
          <el-input v-model="dict.type" :placeholder="$t('sysDict.type')" />
        </el-form-item>
        <el-form-item v-if="dict.parentId !== 0" :label="$t('sysDict.code')" prop="code">
          <el-input v-model="dict.code" placeholder="对应数据库存在的值" />
        </el-form-item>
        <el-form-item v-if="dict.parentId !== 0" :label="$t('sysDict.value')" prop="value">
          <el-input v-model="dict.value" placeholder="对应页面展示文案" />
        </el-form-item>
        <el-form-item v-if="dict.parentId !== 0" :label="$t('sysDict.orderNum')" prop="orderNum">
          <el-input-number v-model="dict.orderNum" :min="1" :label="$t('sysDict.orderNum')" />
        </el-form-item>
        <el-form-item :label="$t('sysDict.remark')" prop="remark">
          <el-input v-model="dict.remark" :placeholder="$t('sysDict.remark')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">{{ $t('table.cancel') }}</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >{{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, del, get, create, update } from '@/api/system/dict'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'

export default {
  name: 'DictTable',
  directives: { waves },
  filters: {},
  data() {
    return {
      list: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      dict: {
        orderNum: 1
      },
      rules: {
        name: [{ required: true, message: '请输入字典名称', trigger: 'blur' }],
        type: [{ required: true, message: '请输入字典类型', trigger: 'blur' }],
        code: [{ required: true, message: '请输入字典编码', trigger: 'blur' }],
        value: [{ required: true, message: '请输入字典值', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.dict = {
          orderNum: 1,
          parentId: 0
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          create(this.dict).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.dict = response
      })
    },
    handleLowerLevel(row) {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()

      this.$nextTick(() => {
        this.dict = {
          orderNum: 1,
          parentId: row.id
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          update(this.dict).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该字典, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      })
    }
  }
}
</script>
