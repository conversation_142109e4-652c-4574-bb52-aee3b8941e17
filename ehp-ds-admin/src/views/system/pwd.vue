<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :model="pwd"
      :rules="rules"
      label-position="right"
      label-width="100px"
      style="width:400px"
    >
      <el-form-item :label="$t('sysUser.password')" prop="password">
        <el-input v-model="pwd.password" :placeholder="$t('sysUser.password')" />
      </el-form-item>
      <el-form-item :label="$t('sysUser.newPassword')" prop="newPassword">
        <el-input v-model="pwd.newPassword" :placeholder="$t('sysUser.newPassword')" />
      </el-form-item>
      <br>
    </el-form>
    <div slot="footer">
      <el-button v-waves @click="dialogFormVisible = false">{{ $t('table.cancel') }}</el-button>
      <el-button v-waves type="primary" @click="updateData">{{ $t('table.confirm') }}</el-button>
    </div>
  </div>
</template>

<script>
import { userPassword } from '@/api/system/user'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'

export default {
  name: 'Pwd',
  directives: { waves },
  data() {
    return {
      pwd: {},
      rules: {
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        newPassword: [
          {
            validator: (rule, value, callback) => {
              var strlc = /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9]))(?=^[^\u4e00-\u9fa5]{0,}$).{8,20}$/
              if (value == null) {
                callback(new Error('请输入新密码'))
              } else if (value.match(strlc) == null) {
                callback(
                  new Error(
                    '密码过于简单有被盗风险，请保证密码大于8位，并且由大小写字母、数字，特殊符号组成'
                  )
                )
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },

  methods: {
    resetTemp() {
      this.$nextTick(() => {
        this.user = {
          status: 1,
          roleIdList: []
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const params = new FormData()
          params.append('password', this.pwd.password)
          params.append('newPassword', this.pwd.newPassword)
          params.append('confirmPass', this.pwd.confirmPass)
          userPassword(params).then(() => {
            this.pwd = {}
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    }
  }
}
</script>
