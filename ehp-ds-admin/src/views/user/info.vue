<template>
  <div class="app-container">
    <el-row :gutter="20" style="font-size:20px;">
      <el-col :span="8" class="fcolor">商家名称：{{ warehouseData.name }}</el-col>
      <el-col :span="8" class="fcolor">商家编码：{{ warehouseData.code }}</el-col>
    </el-row>
    <el-divider></el-divider>
    <div class="fcolor" style="padding-bottom: 20px;">基本信息</div>
    <div class="box">
      <el-row :gutter="20">
        <el-col :span="24" class="fcolor">
          商家名称:{{ warehouseData.name }}
        </el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:20px;padding-bottom:20px;">
        <el-col :span="8" class="fcolor">
          商家联系人:{{ warehouseData.linkman }}
        </el-col>
        <el-col :span="8" class="fcolor">
          联系电话:{{ warehouseData.phone }}
        </el-col>
        <el-col :span="8" class="fcolor">
          联系邮箱:{{ warehouseData.email }}
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="fcolor">
          商家地址:{{ warehouseData.address }}
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<style scoped>
.fcolor{color: #606266;}
.box{background-color: #e8f4ff;padding:20px;}
</style>
<script>
import api_user from '@/api/user/index'
export default {
  'name': '商家信息详情',
  data() {
    return {
      warehouseData: {}
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      api_user.info().then(response => {
        this.warehouseData = response
      })
    }
  }
}
</script>
