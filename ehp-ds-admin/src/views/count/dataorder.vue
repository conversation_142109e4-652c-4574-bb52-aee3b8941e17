<template>
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.orderSn"
        clearable
        placeholder="订单号"
        style="width: 180px"
        type="textarea"
        :rows="4"
      />
      <el-input
        v-model="listQuery.skuNumber"
        clearable
        placeholder="sku码"
        style="width: 150px"
        type="textarea"
        :rows="4"
      />
      <DatePicker
        :query-model="listQuery"
        style="width: 250px"
        start-placeholder="下单开始时间"
        end-placeholder="下单结束时间"
        @change="handleFilter"
      />
    </div>
    <div class="filter-container">
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
      <el-button type="primary" @click="dataOut">导出</el-button>
    </div>
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane name="order" label="订单统计">
        <el-table :key="tableKey1" :data="list1" fit highlight-current-row>
          <el-table-column label="订单号" prop="orderSn" align="center" />
          <el-table-column label="订单金额" prop="totalAmount" align="center" />
          <el-table-column label="下单日期" prop="createdAt" align="center" />
          <!-- <el-table-column label="发货日期" prop="skuName" align="center" /> -->
          <el-table-column label="快递公司" prop="expressName" align="center" />
          <el-table-column label="快递单号" prop="expressCode" align="center" />
          <el-table-column label="订单状态" prop="orderStatusDescribe" align="center" />
        </el-table>
        <pagination
          v-show="total1 > 0"
          :total="total1"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane name="product" label="商品统计">
        <el-table :key="tableKey2" :data="list2" fit highlight-current-row>
          <el-table-column label="SKU码" prop="skuNumber" align="center" />
          <el-table-column label="商品名称" prop="skuName" align="center" />
          <el-table-column label="实付金额" prop="salePrice" align="center" />
          <el-table-column label="数量" prop="quantity" align="center" />
        </el-table>
        <pagination
          v-show="total2 > 0"
          :total="total2"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
import { getToken, getTokenName } from '@/utils/auth'
import api_count from '@/api/count/count'
import DatePicker from '@/components/DatePicker'
export default {
  name: 'Countdataorder',
  filters: {},
  components: {
    DatePicker
  },
  data() {
    return {
      list1: [],
      total1: 0,
      list2: [],
      total2: 0,
      tableKey1: 1,
      tableKey2: 2,
      listQuery: {
        pageNo: 1,
        pageSize: 20
      },
      activeName: 'order'
    }
  },
  created() {
    // this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      if (this.activeName === 'order') {
        api_count.orderList(this.listQuery).then(response => {
          this.list1 = response.list
          this.total1 = response.totalCount
        })
      } else if (this.activeName === 'product') {
        api_count.productList(this.listQuery).then(response => {
          this.list2 = response.list
          this.total2 = response.totalCount
        })
      }
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    dataOut() {
      const queryExport = this.listQuery
      delete queryExport.pageNo
      delete queryExport.pageSize
      queryExport[getTokenName()] = getToken()
      if (this.activeName === 'order') {
        const url = api_count.orderExport()
        window.location.href = url + this.urlEncode(queryExport)
      } else if (this.activeName === 'product') {
        const url = api_count.productExport()
        window.location.href = url + this.urlEncode(queryExport)
      }
    },
    urlEncode(param, key, encode) {
      if (param === null) return ''
      var paramStr = ''
      var t = typeof param
      if (t === 'string' || t === 'number' || t === 'boolean') {
        paramStr +=
          '&' +
          key +
          '=' +
          (encode === null || encode ? encodeURIComponent(param) : param)
      } else {
        for (var i in param) {
          var k =
            key === undefined
              ? i
              : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
          paramStr += this.urlEncode(param[i], k, encode)
        }
      }
      return paramStr
    },
    handleClick(tab, event) {
      this.activeName = tab.name
    }
  }
}
</script>
