<template>
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.skuNumber"
        clearable
        placeholder="订单号"
        style="width: 150px"
        type="textarea"
        :rows="4"
      />
      <el-input
        v-model="listQuery.skuName"
        clearable
        placeholder="收货手机号"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.warehouseName"
        clearable
        placeholder="收货人姓名"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker :query-model="listQuery" style="width: 250px" start-placeholder="下单开始时间" end-placeholder="下单结束时间" @change="handleFilter" />
      <DatePicker :query-model="listQuery" style="width: 250px" start-placeholder="发货开始时间" end-placeholder="发货结束时间" @change="handleFilter" />
      <DictSelect
        v-model="listQuery.dataIntegrity"
        placeholder="订单状态"
        style="width: 150px;"
        type="product_data_integrity"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.skuName"
        clearable
        placeholder="商品sku码"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.warehouseName"
        clearable
        placeholder="商品名称"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
    </div>
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >查询</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >批量确认</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >批量拒绝</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >批量发货</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >订单导出</el-button>
    </div>
    <el-table
      ref="goodsTable"
      :key="tableKey"
      :data="goodsList"
      border
      fit
      highlight-current-row
      @selection-change="goodsSelectionChange"
    >
      <el-table-column type="selection" align="center" width="50"></el-table-column>
      <el-table-column label="操作" align="center">
        <template>
          <el-button type="text">订单确认</el-button>
          <el-button type="text">订单拒绝</el-button>
          <el-button type="text">订单查看</el-button>
          <el-button type="text">打印面单</el-button>
        </template>
      </el-table-column>
      <el-table-column label="订单编号" prop="name" align="center" width="150" />
      <el-table-column label="下单时间" prop="pharmacologyClassificationName" align="center" width="140" />
      <el-table-column label="收货手机" prop="salePrice" align="center" width="80" />
      <el-table-column label="订单状态" prop="salePrice" align="center" width="80" />
      <el-table-column label="订单应付金额" prop="salePrice" align="center" width="110" />
      <el-table-column label="订单实付金额" prop="salePrice" align="center" width="110" />
      <el-table-column label="收件人姓名" prop="salePrice" align="center" width="100" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style scoped>
.filter-container>div{float:left;margin-bottom:10px;margin-right:5px;}
</style>
<script>
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
export default {
  'name': '订单管理',
  components: {
    DictSelect,
    DatePicker
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20
      }
    }
  },
  created() {
  },
  methods: {

  }
}
</script>
