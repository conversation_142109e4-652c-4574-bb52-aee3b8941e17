<template>
  <div class="app-container">
    <el-tabs type="border-card">
      <el-tab-pane label="订单统计" style="line-height:30px;">
        <div>
          今日新增订单
          <span class="red">{{ dashData.toDayQuantity }}</span>个
        </div>
        <div>
          本周新增订单
          <span class="red">{{ dashData.weekQuantity }}</span>个
        </div>
        <div>
          本月新增订单
          <span class="red">{{ dashData.monthQuantity }}</span>个
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style scoped>
.red {
  color: #f00;
}
</style>
<script>
import api_dashboard from '@/api/warehouse/dashboard'
export default {
  name: '',
  data() {
    return {
      dashData: {}
    }
  },
  created() {
    this.getStatistical()
  },
  mounted() {
    setInterval(this.queryOrder, 300000)
  },
  methods: {
    getStatistical() {
      api_dashboard.statistical().then(response => {
        this.dashData = response
      })
    },
    // 定时查询5分钟内的订单
    queryOrder() {
      api_dashboard.queryOrder().then(response => {
        if (response === true) {
          this.play()
        }
      })
    },
    play() {
      // midify by sunlc 2022年7月8日 注释掉声音提示
      // var audio = new Audio('/static/order-tip.mp3')
      this.$notify({
        type: 'success',
        title: '系统消息',
        message: '您有新的订单，请及时确认！',
        position: 'bottom-right',
        duration: 10000
      })
      // audio.play()
    }
  }
}
</script>

