<template>
  <div class="app-container">
    <div class="filter-container clearfix">
      <el-input
        v-model="listQuery.skuName"
        clearable
        placeholder="商品SKU码"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.warehouseName"
        clearable
        placeholder="通用名"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.dataIntegrity"
        placeholder="商品基础分类"
        style="width: 150px;"
        type="product_data_integrity"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.dataIntegrity"
        placeholder="是否启用"
        style="width: 150px;"
        type="product_data_integrity"
        @change="handleFilter"
      />
    </div>
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >查询</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >修改库存</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleFilter"
      >批量修改库存</el-button>
    </div>
    <el-table
      ref="goodsTable"
      :key="tableKey"
      :data="goodsList"
      border
      fit
      highlight-current-row
    >
      <el-table-column label="商品sku码" prop="name" align="center" />
      <el-table-column label="商品名称" prop="pharmacologyClassificationName" align="center" />
      <el-table-column label="通用名" prop="salePrice" align="center" />
      <el-table-column label="商品基础分类" prop="salePrice" align="center" />
      <el-table-column label="库存" prop="salePrice" align="center" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style>

</style>
<script>
import DictSelect from '@/components/DictSelect'
export default {
  'name': 'OrderDetail',
  components: {
    DictSelect
  },
  data() {
    return {
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20
      }
    }
  },
  created() {
  },
  methods: {

  }
}
</script>
