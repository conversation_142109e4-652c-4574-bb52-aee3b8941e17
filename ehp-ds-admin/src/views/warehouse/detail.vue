<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
      >返回</el-button>
      <el-button
        v-waves
        type="primary"
      >确认发货</el-button>
    </div>
    <div class="dtitle">基本信息</div>
    <div class="imgbox" style="padding:20px; font-size: 14px;margin-bottom:10px;">
      <span class="fcolor">物流公司&nbsp;</span>
      <DictSelect
        placeholder="请选择物流公司"
        style="width: 150px;"
        type="product_data_integrity"
        @change="handleFilter"
      />
      <span class="fcolor">&nbsp;&nbsp;物流单号&nbsp;</span>
      <el-input
        style="width:150px;"
        clearable
        @keyup.enter.native="handleFilter"
      />
    </div>
    <div class="dtitle">基本信息</div>
    <table cellspacing="1" cellpadding="0" border="0">
      <tr><td width="10%">订单号</td><td width="35%">16052114313086178779</td><td width="10%">订单状态</td><td width="35%">订单待发货</td></tr>
      <tr><td>订单号</td><td>16052114313086178779</td><td>订单状态</td><td>订单待发货</td></tr>
      <tr><td>用户名</td><td>16052114313086178779</td><td>下单时间</td><td>订单待发货</td></tr>
      <tr><td>支付方式</td><td>16052114313086178779</td><td>付款时间</td><td>订单待发货</td></tr>
      <tr><td>配送方式</td><td>16052114313086178779</td><td>发货时间</td><td>订单待发货</td></tr>
      <tr><td>发货单号</td><td>16052114313086178779</td><td></td><td></td></tr>
    </table>
    <div class="dtitle">发票信息</div>
    <table cellspacing="1" cellpadding="0" border="0">
      <tr><td width="10%">发票抬头</td><td width="35%">16052114313086178779</td><td width="10%">订单状态</td><td width="35%">订单待发货</td></tr>
      <tr><td>用户备注</td><td>16052114313086178779</td><td>客服备注</td><td>订单待发货</td></tr>
    </table>
    <div class="dtitle">收货人信息</div>
    <table cellspacing="1" cellpadding="0" border="0">
      <tr><td width="10%">收货人</td><td width="35%">16052114313086178779</td><td width="10%">手机</td><td width="35%">订单待发货</td></tr>
      <tr><td>地址</td><td>16052114313086178779</td><td>订单状态</td><td>订单待发货</td></tr>
      <tr><td>客户留言</td><td>16052114313086178779</td><td></td><td></td></tr>
    </table>
    <div class="dtitle">商品信息</div>
    <el-table
      ref="goodsTable"
      :key="tableKey"
      :data="goodsList"
      border
      fit
      highlight-current-row
      style="margin-bottom: 10px;"
      @selection-change="goodsSelectionChange"
    >
      <el-table-column label="商品名称" prop="number" align="center" />
      <el-table-column label="商品sku码" prop="name" align="center" />
      <el-table-column label="商品规格" prop="pharmacologyClassificationName" align="center" />
      <el-table-column label="销售单价" prop="salePrice" align="center" />
      <el-table-column label="实付单价" prop="salePrice" align="center" />
      <el-table-column label="商品数量" prop="salePrice" align="center" />
      <el-table-column label="合计实付" prop="salePrice" align="center" />
    </el-table>
    <div class="dtitle">处方信息</div>
    <div class="imgbox" style="margin-bottom: 10px;"><img src="" alt=""></div>
    <div class="dtitle">订单费用信息</div>
    <div class="imgbox">
      <div class="totalprice line">商品总金额：￥ <span class="blue">126.58</span> 元 + 物流成本：￥ <span class="blue">0.00</span> 元</div>
      <div class="totalprice">= 订单总金额：￥ <span class="red">126.58</span> 元</div>
    </div>
  </div>
</template>
<style scoped>
.dtitle{background-color: #F5F7FA;font-size: 14px;padding:0 20px;font-weight: 500;line-height:40px;
    border: 1px solid #dfe4ed;border-bottom:none;}
table{width:100%;background-color:#dfe6ec;text-align:center;margin-bottom:10px;font-size:14px;color:#909399;}
table tr{}
table tr td{background-color:#fff; padding:10px;}
.imgbox{border:1px solid #dfe6ec;}
.imgbox .line{border-bottom:1px solid #dfe6ec;}
.totalprice{padding:0 10px;line-height:40px;text-align:right;font-size:14px;color:#909399;}
.totalprice .red{color:#f00;}
.totalprice .blue{color:blue;}
</style>
<script>
import DictSelect from '@/components/DictSelect'
export default {
  'name': 'OrderDetail',
  components: {
    DictSelect
  },
  data() {
    return {

    }
  },
  created() {
  },
  methods: {

  }
}
</script>
