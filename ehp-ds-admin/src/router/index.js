import Vue from 'vue'
import Router from 'vue-router'
import { switchHump, getFunctionName } from '@/utils'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noredirect           if `redirect:noredirect` will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'             the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/upload-mobile',
    component: () => import('@/views/mobile/upload'),
    hidden: true,
    meta: {
      title: '移动端上传',
      requiresAuth: false
    }
  },
  {
    path: '/404',
    component: () => import('@/views/errorPage/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/errorPage/401'),
    hidden: true
  },
  {
    path: '',
    component: Layout,
    redirect: 'warehouse/dashboard',
    hidden: true,
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/warehouse/dashboard'),
        name: '首页',
        meta: {
          title: '首页',
          icon: 'dashboard',
          noCache: true,
          affix: false
        }
      }
    ]
  },
  {
    path: '/update/pwd',
    component: Layout,
    redirect: '/update/pwd',
    hidden: true,
    children: [
      {
        path: '/update/pwd',
        component: () => import('@/views/system/pwd'),
        name: '修改密码',
        meta: {
          title: '修改密码',
          icon: 'password',
          noCache: true,
          affix: false
        }
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [{ path: '*', redirect: '/404', hidden: true }]

export function restAsyncRoutes() {
  asyncRoutes.splice(1, asyncRoutes.length)
}

export const componentMap = {
  '/system/user': () => import('@/views/system/user'),
  '/system/dept': () => import('@/views/system/dept'),
  '/system/role': () => import('@/views/system/role'),
  '/system/menu': () => import('@/views/system/menu'),
  '/system/dict': () => import('@/views/system/dict'),
  '/system/log': () => import('@/views/system/log'),
  '/warehouse/dashboard': () => import('@/views/warehouse/dashboard'),
  '/order/list': () => import('@/views/order/list'),
  '/order/detail/:orderId': () => import('@/views/order/detail'),
  '/order/sendgood/:orderId': () => import('@/views/order/sendgood'),
  '/product/list': () => import('@/views/product/list'),
  '/user/info': () => import('@/views/user/info'),
  '/count/dataorder': () => import('@/views/count/dataorder'),
  '/exchange/list': () => import('@/views/exchange/list'),
  '/exchange/retreat/:orderId': () => import('@/views/exchange/retreat'),
  '/exchange/change/:orderId': () => import('@/views/exchange/change'),
  '/exchange/repair/:orderId': () => import('@/views/exchange/repair'),
  '/cnMedicine/list': () => import('@/views/cnMedicine/list')
}

export function generaMenu(accessedRoutes, data, parentUrl) {
  if (data) {
    data.forEach(item => {
      const menu = {
        path: item.url + '',
        component: Layout,
        name: item.name,
        hidden: item.hidden === 0 ? false : true,
        meta: {
          title: item.name,
          icon: item.icon,
          noCache: false
        }
      }
      // 有子菜单
      if (item.children) {
        menu.redirect = 'noredirect'
        menu.children = []
        generaMenu(menu.children, item.children, item.url)
      } else {
        // 注册加载相应路由
        menu.component = componentMap[parentUrl + '/' + item.url]
        menu.name = switchHump(getFunctionName(menu.component))
        // console.log(menu.name, menu.component.name, 'menu.component')
      }
      // 将菜单push进路由
      accessedRoutes.push(menu)
    })
  }
}

const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  restAsyncRoutes()
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
